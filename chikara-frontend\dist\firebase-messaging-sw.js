const At=()=>{};var Se={};/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Ke=function(t){const e=[];let n=0;for(let s=0;s<t.length;s++){let r=t.charCodeAt(s);r<128?e[n++]=r:r<2048?(e[n++]=r>>6|192,e[n++]=r&63|128):(r&64512)===55296&&s+1<t.length&&(t.charCodeAt(s+1)&64512)===56320?(r=65536+((r&1023)<<10)+(t.charCodeAt(++s)&1023),e[n++]=r>>18|240,e[n++]=r>>12&63|128,e[n++]=r>>6&63|128,e[n++]=r&63|128):(e[n++]=r>>12|224,e[n++]=r>>6&63|128,e[n++]=r&63|128)}return e},kt=function(t){const e=[];let n=0,s=0;for(;n<t.length;){const r=t[n++];if(r<128)e[s++]=String.fromCharCode(r);else if(r>191&&r<224){const i=t[n++];e[s++]=String.fromCharCode((r&31)<<6|i&63)}else if(r>239&&r<365){const i=t[n++],a=t[n++],o=t[n++],c=((r&7)<<18|(i&63)<<12|(a&63)<<6|o&63)-65536;e[s++]=String.fromCharCode(55296+(c>>10)),e[s++]=String.fromCharCode(56320+(c&1023))}else{const i=t[n++],a=t[n++];e[s++]=String.fromCharCode((r&15)<<12|(i&63)<<6|a&63)}}return e.join("")},Ve={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:typeof atob=="function",encodeByteArray(t,e){if(!Array.isArray(t))throw Error("encodeByteArray takes an array as a parameter");this.init_();const n=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,s=[];for(let r=0;r<t.length;r+=3){const i=t[r],a=r+1<t.length,o=a?t[r+1]:0,c=r+2<t.length,l=c?t[r+2]:0,u=i>>2,d=(i&3)<<4|o>>4;let v=(o&15)<<2|l>>6,L=l&63;c||(L=64,a||(v=64)),s.push(n[u],n[d],n[v],n[L])}return s.join("")},encodeString(t,e){return this.HAS_NATIVE_SUPPORT&&!e?btoa(t):this.encodeByteArray(Ke(t),e)},decodeString(t,e){return this.HAS_NATIVE_SUPPORT&&!e?atob(t):kt(this.decodeStringToByteArray(t,e))},decodeStringToByteArray(t,e){this.init_();const n=e?this.charToByteMapWebSafe_:this.charToByteMap_,s=[];for(let r=0;r<t.length;){const i=n[t.charAt(r++)],o=r<t.length?n[t.charAt(r)]:0;++r;const l=r<t.length?n[t.charAt(r)]:64;++r;const d=r<t.length?n[t.charAt(r)]:64;if(++r,i==null||o==null||l==null||d==null)throw new Rt;const v=i<<2|o>>4;if(s.push(v),l!==64){const L=o<<4&240|l>>2;if(s.push(L),d!==64){const Tt=l<<6&192|d;s.push(Tt)}}}return s},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let t=0;t<this.ENCODED_VALS.length;t++)this.byteToCharMap_[t]=this.ENCODED_VALS.charAt(t),this.charToByteMap_[this.byteToCharMap_[t]]=t,this.byteToCharMapWebSafe_[t]=this.ENCODED_VALS_WEBSAFE.charAt(t),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[t]]=t,t>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(t)]=t,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(t)]=t)}}};class Rt extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}const Dt=function(t){const e=Ke(t);return Ve.encodeByteArray(e,!0)},We=function(t){return Dt(t).replace(/\./g,"")},Ot=function(t){try{return Ve.decodeString(t,!0)}catch(e){console.error("base64Decode failed: ",e)}return null};/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Mt(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("Unable to locate global object.")}/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Nt=()=>Mt().__FIREBASE_DEFAULTS__,Pt=()=>{if(typeof process>"u"||typeof Se>"u")return;const t=Se.__FIREBASE_DEFAULTS__;if(t)return JSON.parse(t)},Lt=()=>{if(typeof document>"u")return;let t;try{t=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch{return}const e=t&&Ot(t[1]);return e&&JSON.parse(e)},Bt=()=>{try{return At()||Nt()||Pt()||Lt()}catch(t){console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${t}`);return}},qe=()=>{var t;return(t=Bt())===null||t===void 0?void 0:t.config};/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let $t=class{constructor(){this.reject=()=>{},this.resolve=()=>{},this.promise=new Promise((e,n)=>{this.resolve=e,this.reject=n})}wrapCallback(e){return(n,s)=>{n?this.reject(n):this.resolve(s),typeof e=="function"&&(this.promise.catch(()=>{}),e.length===1?e(n):e(n,s))}}};function ze(){try{return typeof indexedDB=="object"}catch{return!1}}function Ge(){return new Promise((t,e)=>{try{let n=!0;const s="validate-browser-context-for-indexeddb-analytics-module",r=self.indexedDB.open(s);r.onsuccess=()=>{r.result.close(),n||self.indexedDB.deleteDatabase(s),t(!0)},r.onupgradeneeded=()=>{n=!1},r.onerror=()=>{var i;e(((i=r.error)===null||i===void 0?void 0:i.message)||"")}}catch(n){e(n)}})}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Ut="FirebaseError";class D extends Error{constructor(e,n,s){super(n),this.code=e,this.customData=s,this.name=Ut,Object.setPrototypeOf(this,D.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,K.prototype.create)}}class K{constructor(e,n,s){this.service=e,this.serviceName=n,this.errors=s}create(e,...n){const s=n[0]||{},r=`${this.service}/${e}`,i=this.errors[e],a=i?xt(i,s):"Error",o=`${this.serviceName}: ${a} (${r}).`;return new D(r,o,s)}}function xt(t,e){return t.replace(Ft,(n,s)=>{const r=e[s];return r!=null?String(r):`<${s}?>`})}const Ft=/\{\$([^}]+)}/g;function ie(t,e){if(t===e)return!0;const n=Object.keys(t),s=Object.keys(e);for(const r of n){if(!s.includes(r))return!1;const i=t[r],a=e[r];if(Te(i)&&Te(a)){if(!ie(i,a))return!1}else if(i!==a)return!1}for(const r of s)if(!n.includes(r))return!1;return!0}function Te(t){return t!==null&&typeof t=="object"}/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Je(t){return t&&t._delegate?t._delegate:t}let $=class{constructor(e,n,s){this.name=e,this.instanceFactory=n,this.type=s,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}};/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const C="[DEFAULT]";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class jt{constructor(e,n){this.name=e,this.container=n,this.component=null,this.instances=new Map,this.instancesDeferred=new Map,this.instancesOptions=new Map,this.onInitCallbacks=new Map}get(e){const n=this.normalizeInstanceIdentifier(e);if(!this.instancesDeferred.has(n)){const s=new $t;if(this.instancesDeferred.set(n,s),this.isInitialized(n)||this.shouldAutoInitialize())try{const r=this.getOrInitializeService({instanceIdentifier:n});r&&s.resolve(r)}catch{}}return this.instancesDeferred.get(n).promise}getImmediate(e){var n;const s=this.normalizeInstanceIdentifier(e?.identifier),r=(n=e?.optional)!==null&&n!==void 0?n:!1;if(this.isInitialized(s)||this.shouldAutoInitialize())try{return this.getOrInitializeService({instanceIdentifier:s})}catch(i){if(r)return null;throw i}else{if(r)return null;throw Error(`Service ${this.name} is not available`)}}getComponent(){return this.component}setComponent(e){if(e.name!==this.name)throw Error(`Mismatching Component ${e.name} for Provider ${this.name}.`);if(this.component)throw Error(`Component for ${this.name} has already been provided`);if(this.component=e,!!this.shouldAutoInitialize()){if(Kt(e))try{this.getOrInitializeService({instanceIdentifier:C})}catch{}for(const[n,s]of this.instancesDeferred.entries()){const r=this.normalizeInstanceIdentifier(n);try{const i=this.getOrInitializeService({instanceIdentifier:r});s.resolve(i)}catch{}}}}clearInstance(e=C){this.instancesDeferred.delete(e),this.instancesOptions.delete(e),this.instances.delete(e)}async delete(){const e=Array.from(this.instances.values());await Promise.all([...e.filter(n=>"INTERNAL"in n).map(n=>n.INTERNAL.delete()),...e.filter(n=>"_delete"in n).map(n=>n._delete())])}isComponentSet(){return this.component!=null}isInitialized(e=C){return this.instances.has(e)}getOptions(e=C){return this.instancesOptions.get(e)||{}}initialize(e={}){const{options:n={}}=e,s=this.normalizeInstanceIdentifier(e.instanceIdentifier);if(this.isInitialized(s))throw Error(`${this.name}(${s}) has already been initialized`);if(!this.isComponentSet())throw Error(`Component ${this.name} has not been registered yet`);const r=this.getOrInitializeService({instanceIdentifier:s,options:n});for(const[i,a]of this.instancesDeferred.entries()){const o=this.normalizeInstanceIdentifier(i);s===o&&a.resolve(r)}return r}onInit(e,n){var s;const r=this.normalizeInstanceIdentifier(n),i=(s=this.onInitCallbacks.get(r))!==null&&s!==void 0?s:new Set;i.add(e),this.onInitCallbacks.set(r,i);const a=this.instances.get(r);return a&&e(a,r),()=>{i.delete(e)}}invokeOnInitCallbacks(e,n){const s=this.onInitCallbacks.get(n);if(s)for(const r of s)try{r(e,n)}catch{}}getOrInitializeService({instanceIdentifier:e,options:n={}}){let s=this.instances.get(e);if(!s&&this.component&&(s=this.component.instanceFactory(this.container,{instanceIdentifier:Ht(e),options:n}),this.instances.set(e,s),this.instancesOptions.set(e,n),this.invokeOnInitCallbacks(s,e),this.component.onInstanceCreated))try{this.component.onInstanceCreated(this.container,e,s)}catch{}return s||null}normalizeInstanceIdentifier(e=C){return this.component?this.component.multipleInstances?e:C:e}shouldAutoInitialize(){return!!this.component&&this.component.instantiationMode!=="EXPLICIT"}}function Ht(t){return t===C?void 0:t}function Kt(t){return t.instantiationMode==="EAGER"}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Vt{constructor(e){this.name=e,this.providers=new Map}addComponent(e){const n=this.getProvider(e.name);if(n.isComponentSet())throw new Error(`Component ${e.name} has already been registered with ${this.name}`);n.setComponent(e)}addOrOverwriteComponent(e){this.getProvider(e.name).isComponentSet()&&this.providers.delete(e.name),this.addComponent(e)}getProvider(e){if(this.providers.has(e))return this.providers.get(e);const n=new jt(e,this);return this.providers.set(e,n),n}getProviders(){return Array.from(this.providers.values())}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */var h;(function(t){t[t.DEBUG=0]="DEBUG",t[t.VERBOSE=1]="VERBOSE",t[t.INFO=2]="INFO",t[t.WARN=3]="WARN",t[t.ERROR=4]="ERROR",t[t.SILENT=5]="SILENT"})(h||(h={}));const Wt={debug:h.DEBUG,verbose:h.VERBOSE,info:h.INFO,warn:h.WARN,error:h.ERROR,silent:h.SILENT},qt=h.INFO,zt={[h.DEBUG]:"log",[h.VERBOSE]:"log",[h.INFO]:"info",[h.WARN]:"warn",[h.ERROR]:"error"},Gt=(t,e,...n)=>{if(e<t.logLevel)return;const s=new Date().toISOString(),r=zt[e];if(r)console[r](`[${s}]  ${t.name}:`,...n);else throw new Error(`Attempted to log a message with an invalid logType (value: ${e})`)};class Jt{constructor(e){this.name=e,this._logLevel=qt,this._logHandler=Gt,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in h))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel=typeof e=="string"?Wt[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if(typeof e!="function")throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,h.DEBUG,...e),this._logHandler(this,h.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,h.VERBOSE,...e),this._logHandler(this,h.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,h.INFO,...e),this._logHandler(this,h.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,h.WARN,...e),this._logHandler(this,h.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,h.ERROR,...e),this._logHandler(this,h.ERROR,...e)}}const Yt=(t,e)=>e.some(n=>t instanceof n);let Ae,ke;function Qt(){return Ae||(Ae=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])}function Xt(){return ke||(ke=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])}const Ye=new WeakMap,ae=new WeakMap,Qe=new WeakMap,z=new WeakMap,de=new WeakMap;function Zt(t){const e=new Promise((n,s)=>{const r=()=>{t.removeEventListener("success",i),t.removeEventListener("error",a)},i=()=>{n(b(t.result)),r()},a=()=>{s(t.error),r()};t.addEventListener("success",i),t.addEventListener("error",a)});return e.then(n=>{n instanceof IDBCursor&&Ye.set(n,t)}).catch(()=>{}),de.set(e,t),e}function en(t){if(ae.has(t))return;const e=new Promise((n,s)=>{const r=()=>{t.removeEventListener("complete",i),t.removeEventListener("error",a),t.removeEventListener("abort",a)},i=()=>{n(),r()},a=()=>{s(t.error||new DOMException("AbortError","AbortError")),r()};t.addEventListener("complete",i),t.addEventListener("error",a),t.addEventListener("abort",a)});ae.set(t,e)}let oe={get(t,e,n){if(t instanceof IDBTransaction){if(e==="done")return ae.get(t);if(e==="objectStoreNames")return t.objectStoreNames||Qe.get(t);if(e==="store")return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return b(t[e])},set(t,e,n){return t[e]=n,!0},has(t,e){return t instanceof IDBTransaction&&(e==="done"||e==="store")?!0:e in t}};function tn(t){oe=t(oe)}function nn(t){return t===IDBDatabase.prototype.transaction&&!("objectStoreNames"in IDBTransaction.prototype)?function(e,...n){const s=t.call(G(this),e,...n);return Qe.set(s,e.sort?e.sort():[e]),b(s)}:Xt().includes(t)?function(...e){return t.apply(G(this),e),b(Ye.get(this))}:function(...e){return b(t.apply(G(this),e))}}function sn(t){return typeof t=="function"?nn(t):(t instanceof IDBTransaction&&en(t),Yt(t,Qt())?new Proxy(t,oe):t)}function b(t){if(t instanceof IDBRequest)return Zt(t);if(z.has(t))return z.get(t);const e=sn(t);return e!==t&&(z.set(t,e),de.set(e,t)),e}const G=t=>de.get(t);function V(t,e,{blocked:n,upgrade:s,blocking:r,terminated:i}={}){const a=indexedDB.open(t,e),o=b(a);return s&&a.addEventListener("upgradeneeded",c=>{s(b(a.result),c.oldVersion,c.newVersion,b(a.transaction),c)}),n&&a.addEventListener("blocked",c=>n(c.oldVersion,c.newVersion,c)),o.then(c=>{i&&c.addEventListener("close",()=>i()),r&&c.addEventListener("versionchange",l=>r(l.oldVersion,l.newVersion,l))}).catch(()=>{}),o}function J(t,{blocked:e}={}){const n=indexedDB.deleteDatabase(t);return e&&n.addEventListener("blocked",s=>e(s.oldVersion,s)),b(n).then(()=>{})}const rn=["get","getKey","getAll","getAllKeys","count"],an=["put","add","delete","clear"],Y=new Map;function Re(t,e){if(!(t instanceof IDBDatabase&&!(e in t)&&typeof e=="string"))return;if(Y.get(e))return Y.get(e);const n=e.replace(/FromIndex$/,""),s=e!==n,r=an.includes(n);if(!(n in(s?IDBIndex:IDBObjectStore).prototype)||!(r||rn.includes(n)))return;const i=async function(a,...o){const c=this.transaction(a,r?"readwrite":"readonly");let l=c.store;return s&&(l=l.index(o.shift())),(await Promise.all([l[n](...o),r&&c.done]))[0]};return Y.set(e,i),i}tn(t=>({...t,get:(e,n,s)=>Re(e,n)||t.get(e,n,s),has:(e,n)=>!!Re(e,n)||t.has(e,n)}));/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class on{constructor(e){this.container=e}getPlatformInfoString(){return this.container.getProviders().map(n=>{if(cn(n)){const s=n.getImmediate();return`${s.library}/${s.version}`}else return null}).filter(n=>n).join(" ")}}function cn(t){const e=t.getComponent();return e?.type==="VERSION"}const ce="@firebase/app",De="0.13.2";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const w=new Jt("@firebase/app"),ln="@firebase/app-compat",un="@firebase/analytics-compat",hn="@firebase/analytics",dn="@firebase/app-check-compat",fn="@firebase/app-check",pn="@firebase/auth",gn="@firebase/auth-compat",mn="@firebase/database",bn="@firebase/data-connect",wn="@firebase/database-compat",yn="@firebase/functions",_n="@firebase/functions-compat",In="@firebase/installations",vn="@firebase/installations-compat",Cn="@firebase/messaging",En="@firebase/messaging-compat",Sn="@firebase/performance",Tn="@firebase/performance-compat",An="@firebase/remote-config",kn="@firebase/remote-config-compat",Rn="@firebase/storage",Dn="@firebase/storage-compat",On="@firebase/firestore",Mn="@firebase/ai",Nn="@firebase/firestore-compat",Pn="firebase";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const le="[DEFAULT]",Ln={[ce]:"fire-core",[ln]:"fire-core-compat",[hn]:"fire-analytics",[un]:"fire-analytics-compat",[fn]:"fire-app-check",[dn]:"fire-app-check-compat",[pn]:"fire-auth",[gn]:"fire-auth-compat",[mn]:"fire-rtdb",[bn]:"fire-data-connect",[wn]:"fire-rtdb-compat",[yn]:"fire-fn",[_n]:"fire-fn-compat",[In]:"fire-iid",[vn]:"fire-iid-compat",[Cn]:"fire-fcm",[En]:"fire-fcm-compat",[Sn]:"fire-perf",[Tn]:"fire-perf-compat",[An]:"fire-rc",[kn]:"fire-rc-compat",[Rn]:"fire-gcs",[Dn]:"fire-gcs-compat",[On]:"fire-fst",[Nn]:"fire-fst-compat",[Mn]:"fire-vertex","fire-js":"fire-js",[Pn]:"fire-js-all"};/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const U=new Map,Bn=new Map,ue=new Map;function Oe(t,e){try{t.container.addComponent(e)}catch(n){w.debug(`Component ${e.name} failed to register with FirebaseApp ${t.name}`,n)}}function R(t){const e=t.name;if(ue.has(e))return w.debug(`There were multiple attempts to register component ${e}.`),!1;ue.set(e,t);for(const n of U.values())Oe(n,t);for(const n of Bn.values())Oe(n,t);return!0}function fe(t,e){const n=t.container.getProvider("heartbeat").getImmediate({optional:!0});return n&&n.triggerHeartbeat(),t.container.getProvider(e)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const $n={"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}'","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","server-app-deleted":"Firebase Server App has been deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.","finalization-registry-not-supported":"FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.","invalid-server-app-environment":"FirebaseServerApp is not for use in browser environments."},I=new K("app","Firebase",$n);/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Un{constructor(e,n,s){this._isDeleted=!1,this._options=Object.assign({},e),this._config=Object.assign({},n),this._name=n.name,this._automaticDataCollectionEnabled=n.automaticDataCollectionEnabled,this._container=s,this.container.addComponent(new $("app",()=>this,"PUBLIC"))}get automaticDataCollectionEnabled(){return this.checkDestroyed(),this._automaticDataCollectionEnabled}set automaticDataCollectionEnabled(e){this.checkDestroyed(),this._automaticDataCollectionEnabled=e}get name(){return this.checkDestroyed(),this._name}get options(){return this.checkDestroyed(),this._options}get config(){return this.checkDestroyed(),this._config}get container(){return this._container}get isDeleted(){return this._isDeleted}set isDeleted(e){this._isDeleted=e}checkDestroyed(){if(this.isDeleted)throw I.create("app-deleted",{appName:this._name})}}function Xe(t,e={}){let n=t;typeof e!="object"&&(e={name:e});const s=Object.assign({name:le,automaticDataCollectionEnabled:!0},e),r=s.name;if(typeof r!="string"||!r)throw I.create("bad-app-name",{appName:String(r)});if(n||(n=qe()),!n)throw I.create("no-options");const i=U.get(r);if(i){if(ie(n,i.options)&&ie(s,i.config))return i;throw I.create("duplicate-app",{appName:r})}const a=new Vt(r);for(const c of ue.values())a.addComponent(c);const o=new Un(n,s,a);return U.set(r,o),o}function xn(t=le){const e=U.get(t);if(!e&&t===le&&qe())return Xe();if(!e)throw I.create("no-app",{appName:t});return e}function k(t,e,n){var s;let r=(s=Ln[t])!==null&&s!==void 0?s:t;n&&(r+=`-${n}`);const i=r.match(/\s|\//),a=e.match(/\s|\//);if(i||a){const o=[`Unable to register library "${r}" with version "${e}":`];i&&o.push(`library name "${r}" contains illegal characters (whitespace or "/")`),i&&a&&o.push("and"),a&&o.push(`version name "${e}" contains illegal characters (whitespace or "/")`),w.warn(o.join(" "));return}R(new $(`${r}-version`,()=>({library:r,version:e}),"VERSION"))}/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Fn="firebase-heartbeat-database",jn=1,P="firebase-heartbeat-store";let Q=null;function Ze(){return Q||(Q=V(Fn,jn,{upgrade:(t,e)=>{switch(e){case 0:try{t.createObjectStore(P)}catch(n){console.warn(n)}}}}).catch(t=>{throw I.create("idb-open",{originalErrorMessage:t.message})})),Q}async function Hn(t){try{const n=(await Ze()).transaction(P),s=await n.objectStore(P).get(et(t));return await n.done,s}catch(e){if(e instanceof D)w.warn(e.message);else{const n=I.create("idb-get",{originalErrorMessage:e?.message});w.warn(n.message)}}}async function Me(t,e){try{const s=(await Ze()).transaction(P,"readwrite");await s.objectStore(P).put(e,et(t)),await s.done}catch(n){if(n instanceof D)w.warn(n.message);else{const s=I.create("idb-set",{originalErrorMessage:n?.message});w.warn(s.message)}}}function et(t){return`${t.name}!${t.options.appId}`}/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Kn=1024,Vn=30;class Wn{constructor(e){this.container=e,this._heartbeatsCache=null;const n=this.container.getProvider("app").getImmediate();this._storage=new zn(n),this._heartbeatsCachePromise=this._storage.read().then(s=>(this._heartbeatsCache=s,s))}async triggerHeartbeat(){var e,n;try{const r=this.container.getProvider("platform-logger").getImmediate().getPlatformInfoString(),i=Ne();if(((e=this._heartbeatsCache)===null||e===void 0?void 0:e.heartbeats)==null&&(this._heartbeatsCache=await this._heartbeatsCachePromise,((n=this._heartbeatsCache)===null||n===void 0?void 0:n.heartbeats)==null)||this._heartbeatsCache.lastSentHeartbeatDate===i||this._heartbeatsCache.heartbeats.some(a=>a.date===i))return;if(this._heartbeatsCache.heartbeats.push({date:i,agent:r}),this._heartbeatsCache.heartbeats.length>Vn){const a=Gn(this._heartbeatsCache.heartbeats);this._heartbeatsCache.heartbeats.splice(a,1)}return this._storage.overwrite(this._heartbeatsCache)}catch(s){w.warn(s)}}async getHeartbeatsHeader(){var e;try{if(this._heartbeatsCache===null&&await this._heartbeatsCachePromise,((e=this._heartbeatsCache)===null||e===void 0?void 0:e.heartbeats)==null||this._heartbeatsCache.heartbeats.length===0)return"";const n=Ne(),{heartbeatsToSend:s,unsentEntries:r}=qn(this._heartbeatsCache.heartbeats),i=We(JSON.stringify({version:2,heartbeats:s}));return this._heartbeatsCache.lastSentHeartbeatDate=n,r.length>0?(this._heartbeatsCache.heartbeats=r,await this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),i}catch(n){return w.warn(n),""}}}function Ne(){return new Date().toISOString().substring(0,10)}function qn(t,e=Kn){const n=[];let s=t.slice();for(const r of t){const i=n.find(a=>a.agent===r.agent);if(i){if(i.dates.push(r.date),Pe(n)>e){i.dates.pop();break}}else if(n.push({agent:r.agent,dates:[r.date]}),Pe(n)>e){n.pop();break}s=s.slice(1)}return{heartbeatsToSend:n,unsentEntries:s}}class zn{constructor(e){this.app=e,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}async runIndexedDBEnvironmentCheck(){return ze()?Ge().then(()=>!0).catch(()=>!1):!1}async read(){if(await this._canUseIndexedDBPromise){const n=await Hn(this.app);return n?.heartbeats?n:{heartbeats:[]}}else return{heartbeats:[]}}async overwrite(e){var n;if(await this._canUseIndexedDBPromise){const r=await this.read();return Me(this.app,{lastSentHeartbeatDate:(n=e.lastSentHeartbeatDate)!==null&&n!==void 0?n:r.lastSentHeartbeatDate,heartbeats:e.heartbeats})}else return}async add(e){var n;if(await this._canUseIndexedDBPromise){const r=await this.read();return Me(this.app,{lastSentHeartbeatDate:(n=e.lastSentHeartbeatDate)!==null&&n!==void 0?n:r.lastSentHeartbeatDate,heartbeats:[...r.heartbeats,...e.heartbeats]})}else return}}function Pe(t){return We(JSON.stringify({version:2,heartbeats:t})).length}function Gn(t){if(t.length===0)return-1;let e=0,n=t[0].date;for(let s=1;s<t.length;s++)t[s].date<n&&(n=t[s].date,e=s);return e}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Jn(t){R(new $("platform-logger",e=>new on(e),"PRIVATE")),R(new $("heartbeat",e=>new Wn(e),"PRIVATE")),k(ce,De,t),k(ce,De,"esm2017"),k("fire-js","")}Jn("");var Yn="firebase",Qn="11.10.0";/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */k(Yn,Qn,"app");let Le=class{constructor(e,n,s){this.name=e,this.instanceFactory=n,this.type=s,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}};const tt="@firebase/installations",pe="0.6.18";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const nt=1e4,st=`w:${pe}`,rt="FIS_v2",Xn="https://firebaseinstallations.googleapis.com/v1",Zn=60*60*1e3,es="installations",ts="Installations";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ns={"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."},S=new K(es,ts,ns);function it(t){return t instanceof D&&t.code.includes("request-failed")}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function at({projectId:t}){return`${Xn}/projects/${t}/installations`}function ot(t){return{token:t.token,requestStatus:2,expiresIn:rs(t.expiresIn),creationTime:Date.now()}}async function ct(t,e){const s=(await e.json()).error;return S.create("request-failed",{requestName:t,serverCode:s.code,serverMessage:s.message,serverStatus:s.status})}function lt({apiKey:t}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":t})}function ss(t,{refreshToken:e}){const n=lt(t);return n.append("Authorization",is(e)),n}async function ut(t){const e=await t();return e.status>=500&&e.status<600?t():e}function rs(t){return Number(t.replace("s","000"))}function is(t){return`${rt} ${t}`}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function as({appConfig:t,heartbeatServiceProvider:e},{fid:n}){const s=at(t),r=lt(t),i=e.getImmediate({optional:!0});if(i){const l=await i.getHeartbeatsHeader();l&&r.append("x-firebase-client",l)}const a={fid:n,authVersion:rt,appId:t.appId,sdkVersion:st},o={method:"POST",headers:r,body:JSON.stringify(a)},c=await ut(()=>fetch(s,o));if(c.ok){const l=await c.json();return{fid:l.fid||n,registrationStatus:2,refreshToken:l.refreshToken,authToken:ot(l.authToken)}}else throw await ct("Create Installation",c)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function ht(t){return new Promise(e=>{setTimeout(e,t)})}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function os(t){return btoa(String.fromCharCode(...t)).replace(/\+/g,"-").replace(/\//g,"_")}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const cs=/^[cdef][\w-]{21}$/,he="";function ls(){try{const t=new Uint8Array(17);(self.crypto||self.msCrypto).getRandomValues(t),t[0]=112+t[0]%16;const n=us(t);return cs.test(n)?n:he}catch{return he}}function us(t){return os(t).substr(0,22)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function W(t){return`${t.appName}!${t.appId}`}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const dt=new Map;function ft(t,e){const n=W(t);pt(n,e),hs(n,e)}function pt(t,e){const n=dt.get(t);if(n)for(const s of n)s(e)}function hs(t,e){const n=ds();n&&n.postMessage({key:t,fid:e}),fs()}let E=null;function ds(){return!E&&"BroadcastChannel"in self&&(E=new BroadcastChannel("[Firebase] FID Change"),E.onmessage=t=>{pt(t.data.key,t.data.fid)}),E}function fs(){dt.size===0&&E&&(E.close(),E=null)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ps="firebase-installations-database",gs=1,T="firebase-installations-store";let X=null;function ge(){return X||(X=V(ps,gs,{upgrade:(t,e)=>{switch(e){case 0:t.createObjectStore(T)}}})),X}async function x(t,e){const n=W(t),r=(await ge()).transaction(T,"readwrite"),i=r.objectStore(T),a=await i.get(n);return await i.put(e,n),await r.done,(!a||a.fid!==e.fid)&&ft(t,e.fid),e}async function gt(t){const e=W(t),s=(await ge()).transaction(T,"readwrite");await s.objectStore(T).delete(e),await s.done}async function q(t,e){const n=W(t),r=(await ge()).transaction(T,"readwrite"),i=r.objectStore(T),a=await i.get(n),o=e(a);return o===void 0?await i.delete(n):await i.put(o,n),await r.done,o&&(!a||a.fid!==o.fid)&&ft(t,o.fid),o}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function me(t){let e;const n=await q(t.appConfig,s=>{const r=ms(s),i=bs(t,r);return e=i.registrationPromise,i.installationEntry});return n.fid===he?{installationEntry:await e}:{installationEntry:n,registrationPromise:e}}function ms(t){const e=t||{fid:ls(),registrationStatus:0};return mt(e)}function bs(t,e){if(e.registrationStatus===0){if(!navigator.onLine){const r=Promise.reject(S.create("app-offline"));return{installationEntry:e,registrationPromise:r}}const n={fid:e.fid,registrationStatus:1,registrationTime:Date.now()},s=ws(t,n);return{installationEntry:n,registrationPromise:s}}else return e.registrationStatus===1?{installationEntry:e,registrationPromise:ys(t)}:{installationEntry:e}}async function ws(t,e){try{const n=await as(t,e);return x(t.appConfig,n)}catch(n){throw it(n)&&n.customData.serverCode===409?await gt(t.appConfig):await x(t.appConfig,{fid:e.fid,registrationStatus:0}),n}}async function ys(t){let e=await Be(t.appConfig);for(;e.registrationStatus===1;)await ht(100),e=await Be(t.appConfig);if(e.registrationStatus===0){const{installationEntry:n,registrationPromise:s}=await me(t);return s||n}return e}function Be(t){return q(t,e=>{if(!e)throw S.create("installation-not-found");return mt(e)})}function mt(t){return _s(t)?{fid:t.fid,registrationStatus:0}:t}function _s(t){return t.registrationStatus===1&&t.registrationTime+nt<Date.now()}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Is({appConfig:t,heartbeatServiceProvider:e},n){const s=vs(t,n),r=ss(t,n),i=e.getImmediate({optional:!0});if(i){const l=await i.getHeartbeatsHeader();l&&r.append("x-firebase-client",l)}const a={installation:{sdkVersion:st,appId:t.appId}},o={method:"POST",headers:r,body:JSON.stringify(a)},c=await ut(()=>fetch(s,o));if(c.ok){const l=await c.json();return ot(l)}else throw await ct("Generate Auth Token",c)}function vs(t,{fid:e}){return`${at(t)}/${e}/authTokens:generate`}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function be(t,e=!1){let n;const s=await q(t.appConfig,i=>{if(!bt(i))throw S.create("not-registered");const a=i.authToken;if(!e&&Ss(a))return i;if(a.requestStatus===1)return n=Cs(t,e),i;{if(!navigator.onLine)throw S.create("app-offline");const o=As(i);return n=Es(t,o),o}});return n?await n:s.authToken}async function Cs(t,e){let n=await $e(t.appConfig);for(;n.authToken.requestStatus===1;)await ht(100),n=await $e(t.appConfig);const s=n.authToken;return s.requestStatus===0?be(t,e):s}function $e(t){return q(t,e=>{if(!bt(e))throw S.create("not-registered");const n=e.authToken;return ks(n)?Object.assign(Object.assign({},e),{authToken:{requestStatus:0}}):e})}async function Es(t,e){try{const n=await Is(t,e),s=Object.assign(Object.assign({},e),{authToken:n});return await x(t.appConfig,s),n}catch(n){if(it(n)&&(n.customData.serverCode===401||n.customData.serverCode===404))await gt(t.appConfig);else{const s=Object.assign(Object.assign({},e),{authToken:{requestStatus:0}});await x(t.appConfig,s)}throw n}}function bt(t){return t!==void 0&&t.registrationStatus===2}function Ss(t){return t.requestStatus===2&&!Ts(t)}function Ts(t){const e=Date.now();return e<t.creationTime||t.creationTime+t.expiresIn<e+Zn}function As(t){const e={requestStatus:1,requestTime:Date.now()};return Object.assign(Object.assign({},t),{authToken:e})}function ks(t){return t.requestStatus===1&&t.requestTime+nt<Date.now()}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Rs(t){const e=t,{installationEntry:n,registrationPromise:s}=await me(e);return s?s.catch(console.error):be(e).catch(console.error),n.fid}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Ds(t,e=!1){const n=t;return await Os(n),(await be(n,e)).token}async function Os(t){const{registrationPromise:e}=await me(t);e&&await e}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Ms(t){if(!t||!t.options)throw Z("App Configuration");if(!t.name)throw Z("App Name");const e=["projectId","apiKey","appId"];for(const n of e)if(!t.options[n])throw Z(n);return{appName:t.name,projectId:t.options.projectId,apiKey:t.options.apiKey,appId:t.options.appId}}function Z(t){return S.create("missing-app-config-values",{valueName:t})}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const wt="installations",Ns="installations-internal",Ps=t=>{const e=t.getProvider("app").getImmediate(),n=Ms(e),s=fe(e,"heartbeat");return{app:e,appConfig:n,heartbeatServiceProvider:s,_delete:()=>Promise.resolve()}},Ls=t=>{const e=t.getProvider("app").getImmediate(),n=fe(e,wt).getImmediate();return{getId:()=>Rs(n),getToken:r=>Ds(n,r)}};function Bs(){R(new Le(wt,Ps,"PUBLIC")),R(new Le(Ns,Ls,"PRIVATE"))}Bs();k(tt,pe);k(tt,pe,"esm2017");class $s{constructor(e,n,s){this.name=e,this.instanceFactory=n,this.type=s,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const yt="BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4",Us="https://fcmregistrations.googleapis.com/v1",_t="FCM_MSG",xs="google.c.a.c_id",Fs=3,js=1;var F;(function(t){t[t.DATA_MESSAGE=1]="DATA_MESSAGE",t[t.DISPLAY_NOTIFICATION=3]="DISPLAY_NOTIFICATION"})(F||(F={}));/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
 * in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 */var j;(function(t){t.PUSH_RECEIVED="push-received",t.NOTIFICATION_CLICKED="notification-clicked"})(j||(j={}));/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function g(t){const e=new Uint8Array(t);return btoa(String.fromCharCode(...e)).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function Hs(t){const e="=".repeat((4-t.length%4)%4),n=(t+e).replace(/\-/g,"+").replace(/_/g,"/"),s=atob(n),r=new Uint8Array(s.length);for(let i=0;i<s.length;++i)r[i]=s.charCodeAt(i);return r}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ee="fcm_token_details_db",Ks=5,Ue="fcm_token_object_Store";async function Vs(t){if("databases"in indexedDB&&!(await indexedDB.databases()).map(i=>i.name).includes(ee))return null;let e=null;return(await V(ee,Ks,{upgrade:async(s,r,i,a)=>{var o;if(r<2||!s.objectStoreNames.contains(Ue))return;const c=a.objectStore(Ue),l=await c.index("fcmSenderId").get(t);if(await c.clear(),!!l){if(r===2){const u=l;if(!u.auth||!u.p256dh||!u.endpoint)return;e={token:u.fcmToken,createTime:(o=u.createTime)!==null&&o!==void 0?o:Date.now(),subscriptionOptions:{auth:u.auth,p256dh:u.p256dh,endpoint:u.endpoint,swScope:u.swScope,vapidKey:typeof u.vapidKey=="string"?u.vapidKey:g(u.vapidKey)}}}else if(r===3){const u=l;e={token:u.fcmToken,createTime:u.createTime,subscriptionOptions:{auth:g(u.auth),p256dh:g(u.p256dh),endpoint:u.endpoint,swScope:u.swScope,vapidKey:g(u.vapidKey)}}}else if(r===4){const u=l;e={token:u.fcmToken,createTime:u.createTime,subscriptionOptions:{auth:g(u.auth),p256dh:g(u.p256dh),endpoint:u.endpoint,swScope:u.swScope,vapidKey:g(u.vapidKey)}}}}}})).close(),await J(ee),await J("fcm_vapid_details_db"),await J("undefined"),Ws(e)?e:null}function Ws(t){if(!t||!t.subscriptionOptions)return!1;const{subscriptionOptions:e}=t;return typeof t.createTime=="number"&&t.createTime>0&&typeof t.token=="string"&&t.token.length>0&&typeof e.auth=="string"&&e.auth.length>0&&typeof e.p256dh=="string"&&e.p256dh.length>0&&typeof e.endpoint=="string"&&e.endpoint.length>0&&typeof e.swScope=="string"&&e.swScope.length>0&&typeof e.vapidKey=="string"&&e.vapidKey.length>0}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const qs="firebase-messaging-database",zs=1,A="firebase-messaging-store";let te=null;function we(){return te||(te=V(qs,zs,{upgrade:(t,e)=>{switch(e){case 0:t.createObjectStore(A)}}})),te}async function ye(t){const e=Ie(t),s=await(await we()).transaction(A).objectStore(A).get(e);if(s)return s;{const r=await Vs(t.appConfig.senderId);if(r)return await _e(t,r),r}}async function _e(t,e){const n=Ie(t),r=(await we()).transaction(A,"readwrite");return await r.objectStore(A).put(e,n),await r.done,e}async function Gs(t){const e=Ie(t),s=(await we()).transaction(A,"readwrite");await s.objectStore(A).delete(e),await s.done}function Ie({appConfig:t}){return t.appId}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Js={"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"only-available-in-window":"This method is available in a Window context.","only-available-in-sw":"This method is available in a service worker context.","permission-default":"The notification permission was not granted and dismissed instead.","permission-blocked":"The notification permission was not granted and blocked instead.","unsupported-browser":"This browser doesn't support the API's required to use the Firebase SDK.","indexed-db-unsupported":"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)","failed-service-worker-registration":"We are unable to register the default service worker. {$browserErrorMessage}","token-subscribe-failed":"A problem occurred while subscribing the user to FCM: {$errorInfo}","token-subscribe-no-token":"FCM returned no token when subscribing the user to push.","token-unsubscribe-failed":"A problem occurred while unsubscribing the user from FCM: {$errorInfo}","token-update-failed":"A problem occurred while updating the user from FCM: {$errorInfo}","token-update-no-token":"FCM returned no token when updating the user to push.","use-sw-after-get-token":"The useServiceWorker() method may only be called once and must be called before calling getToken() to ensure your service worker is used.","invalid-sw-registration":"The input to useServiceWorker() must be a ServiceWorkerRegistration.","invalid-bg-handler":"The input to setBackgroundMessageHandler() must be a function.","invalid-vapid-key":"The public VAPID key must be a string.","use-vapid-key-after-get-token":"The usePublicVapidKey() method may only be called once and must be called before calling getToken() to ensure your VAPID key is used."},p=new K("messaging","Messaging",Js);/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Ys(t,e){const n=await Ce(t),s=vt(e),r={method:"POST",headers:n,body:JSON.stringify(s)};let i;try{i=await(await fetch(ve(t.appConfig),r)).json()}catch(a){throw p.create("token-subscribe-failed",{errorInfo:a?.toString()})}if(i.error){const a=i.error.message;throw p.create("token-subscribe-failed",{errorInfo:a})}if(!i.token)throw p.create("token-subscribe-no-token");return i.token}async function Qs(t,e){const n=await Ce(t),s=vt(e.subscriptionOptions),r={method:"PATCH",headers:n,body:JSON.stringify(s)};let i;try{i=await(await fetch(`${ve(t.appConfig)}/${e.token}`,r)).json()}catch(a){throw p.create("token-update-failed",{errorInfo:a?.toString()})}if(i.error){const a=i.error.message;throw p.create("token-update-failed",{errorInfo:a})}if(!i.token)throw p.create("token-update-no-token");return i.token}async function It(t,e){const s={method:"DELETE",headers:await Ce(t)};try{const i=await(await fetch(`${ve(t.appConfig)}/${e}`,s)).json();if(i.error){const a=i.error.message;throw p.create("token-unsubscribe-failed",{errorInfo:a})}}catch(r){throw p.create("token-unsubscribe-failed",{errorInfo:r?.toString()})}}function ve({projectId:t}){return`${Us}/projects/${t}/registrations`}async function Ce({appConfig:t,installations:e}){const n=await e.getToken();return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":t.apiKey,"x-goog-firebase-installations-auth":`FIS ${n}`})}function vt({p256dh:t,auth:e,endpoint:n,vapidKey:s}){const r={web:{endpoint:n,auth:e,p256dh:t}};return s!==yt&&(r.web.applicationPubKey=s),r}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Xs=7*24*60*60*1e3;async function Zs(t){const e=await tr(t.swRegistration,t.vapidKey),n={vapidKey:t.vapidKey,swScope:t.swRegistration.scope,endpoint:e.endpoint,auth:g(e.getKey("auth")),p256dh:g(e.getKey("p256dh"))},s=await ye(t.firebaseDependencies);if(s){if(nr(s.subscriptionOptions,n))return Date.now()>=s.createTime+Xs?er(t,{token:s.token,createTime:Date.now(),subscriptionOptions:n}):s.token;try{await It(t.firebaseDependencies,s.token)}catch(r){console.warn(r)}return Fe(t.firebaseDependencies,n)}else return Fe(t.firebaseDependencies,n)}async function xe(t){const e=await ye(t.firebaseDependencies);e&&(await It(t.firebaseDependencies,e.token),await Gs(t.firebaseDependencies));const n=await t.swRegistration.pushManager.getSubscription();return n?n.unsubscribe():!0}async function er(t,e){try{const n=await Qs(t.firebaseDependencies,e),s=Object.assign(Object.assign({},e),{token:n,createTime:Date.now()});return await _e(t.firebaseDependencies,s),n}catch(n){throw n}}async function Fe(t,e){const s={token:await Ys(t,e),createTime:Date.now(),subscriptionOptions:e};return await _e(t,s),s.token}async function tr(t,e){const n=await t.pushManager.getSubscription();return n||t.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:Hs(e)})}function nr(t,e){const n=e.vapidKey===t.vapidKey,s=e.endpoint===t.endpoint,r=e.auth===t.auth,i=e.p256dh===t.p256dh;return n&&s&&r&&i}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function sr(t){const e={from:t.from,collapseKey:t.collapse_key,messageId:t.fcmMessageId};return rr(e,t),ir(e,t),ar(e,t),e}function rr(t,e){if(!e.notification)return;t.notification={};const n=e.notification.title;n&&(t.notification.title=n);const s=e.notification.body;s&&(t.notification.body=s);const r=e.notification.image;r&&(t.notification.image=r);const i=e.notification.icon;i&&(t.notification.icon=i)}function ir(t,e){e.data&&(t.data=e.data)}function ar(t,e){var n,s,r,i,a;if(!e.fcmOptions&&!(!((n=e.notification)===null||n===void 0)&&n.click_action))return;t.fcmOptions={};const o=(r=(s=e.fcmOptions)===null||s===void 0?void 0:s.link)!==null&&r!==void 0?r:(i=e.notification)===null||i===void 0?void 0:i.click_action;o&&(t.fcmOptions.link=o);const c=(a=e.fcmOptions)===null||a===void 0?void 0:a.analytics_label;c&&(t.fcmOptions.analyticsLabel=c)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function or(t){return typeof t=="object"&&!!t&&xs in t}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function cr(t){return new Promise(e=>{setTimeout(e,t)})}async function lr(t,e){const n=ur(e,await t.firebaseDependencies.installations.getId());hr(t,n,e.productId)}function ur(t,e){var n,s;const r={};return t.from&&(r.project_number=t.from),t.fcmMessageId&&(r.message_id=t.fcmMessageId),r.instance_id=e,t.notification?r.message_type=F.DISPLAY_NOTIFICATION.toString():r.message_type=F.DATA_MESSAGE.toString(),r.sdk_platform=Fs.toString(),r.package_name=self.origin.replace(/(^\w+:|^)\/\//,""),t.collapse_key&&(r.collapse_key=t.collapse_key),r.event=js.toString(),!((n=t.fcmOptions)===null||n===void 0)&&n.analytics_label&&(r.analytics_label=(s=t.fcmOptions)===null||s===void 0?void 0:s.analytics_label),r}function hr(t,e,n){const s={};s.event_time_ms=Math.floor(Date.now()).toString(),s.source_extension_json_proto3=JSON.stringify({messaging_client_event:e}),n&&(s.compliance_data=dr(n)),t.logEvents.push(s)}function dr(t){return{privacy_context:{prequest:{origin_associated_product_id:t}}}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function fr(t,e){var n,s;const{newSubscription:r}=t;if(!r){await xe(e);return}const i=await ye(e.firebaseDependencies);await xe(e),e.vapidKey=(s=(n=i?.subscriptionOptions)===null||n===void 0?void 0:n.vapidKey)!==null&&s!==void 0?s:yt,await Zs(e)}async function pr(t,e){const n=br(t);if(!n)return;e.deliveryMetricsExportedToBigQueryEnabled&&await lr(e,n);const s=await Ct();if(yr(s))return _r(s,n);if(n.notification&&await Ir(mr(n)),!!e&&e.onBackgroundMessageHandler){const r=sr(n);typeof e.onBackgroundMessageHandler=="function"?await e.onBackgroundMessageHandler(r):e.onBackgroundMessageHandler.next(r)}}async function gr(t){var e,n;const s=(n=(e=t.notification)===null||e===void 0?void 0:e.data)===null||n===void 0?void 0:n[_t];if(s){if(t.action)return}else return;t.stopImmediatePropagation(),t.notification.close();const r=vr(s);if(!r)return;const i=new URL(r,self.location.href),a=new URL(self.location.origin);if(i.host!==a.host)return;let o=await wr(i);if(o?o=await o.focus():(o=await self.clients.openWindow(r),await cr(3e3)),!!o)return s.messageType=j.NOTIFICATION_CLICKED,s.isFirebaseMessaging=!0,o.postMessage(s)}function mr(t){const e=Object.assign({},t.notification);return e.data={[_t]:t},e}function br({data:t}){if(!t)return null;try{return t.json()}catch{return null}}async function wr(t){const e=await Ct();for(const n of e){const s=new URL(n.url,self.location.href);if(t.host===s.host)return n}return null}function yr(t){return t.some(e=>e.visibilityState==="visible"&&!e.url.startsWith("chrome-extension://"))}function _r(t,e){e.isFirebaseMessaging=!0,e.messageType=j.PUSH_RECEIVED;for(const n of t)n.postMessage(e)}function Ct(){return self.clients.matchAll({type:"window",includeUncontrolled:!0})}function Ir(t){var e;const{actions:n}=t,{maxActions:s}=Notification;return n&&s&&n.length>s&&console.warn(`This browser only supports ${s} actions. The remaining actions will not be displayed.`),self.registration.showNotification((e=t.title)!==null&&e!==void 0?e:"",t)}function vr(t){var e,n,s;const r=(n=(e=t.fcmOptions)===null||e===void 0?void 0:e.link)!==null&&n!==void 0?n:(s=t.notification)===null||s===void 0?void 0:s.click_action;return r||(or(t.data)?self.location.origin:null)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Cr(t){if(!t||!t.options)throw ne("App Configuration Object");if(!t.name)throw ne("App Name");const e=["projectId","apiKey","appId","messagingSenderId"],{options:n}=t;for(const s of e)if(!n[s])throw ne(s);return{appName:t.name,projectId:n.projectId,apiKey:n.apiKey,appId:n.appId,senderId:n.messagingSenderId}}function ne(t){return p.create("missing-app-config-values",{valueName:t})}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Er{constructor(e,n,s){this.deliveryMetricsExportedToBigQueryEnabled=!1,this.onBackgroundMessageHandler=null,this.onMessageHandler=null,this.logEvents=[],this.isLogServiceStarted=!1;const r=Cr(e);this.firebaseDependencies={app:e,appConfig:r,installations:n,analyticsProvider:s}}_delete(){return Promise.resolve()}}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Sr=t=>{const e=new Er(t.getProvider("app").getImmediate(),t.getProvider("installations-internal").getImmediate(),t.getProvider("analytics-internal"));return self.addEventListener("push",n=>{n.waitUntil(pr(n,e))}),self.addEventListener("pushsubscriptionchange",n=>{n.waitUntil(fr(n,e))}),self.addEventListener("notificationclick",n=>{n.waitUntil(gr(n))}),e};function Tr(){R(new $s("messaging-sw",Sr,"PUBLIC"))}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Ar(){return ze()&&await Ge()&&"PushManager"in self&&"Notification"in self&&ServiceWorkerRegistration.prototype.hasOwnProperty("showNotification")&&PushSubscription.prototype.hasOwnProperty("getKey")}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function kr(t,e){if(self.document!==void 0)throw p.create("only-available-in-sw");return t.onBackgroundMessageHandler=e,()=>{t.onBackgroundMessageHandler=null}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Rr(t=xn()){return Ar().then(e=>{if(!e)throw p.create("unsupported-browser")},e=>{throw p.create("indexed-db-unsupported")}),fe(Je(t),"messaging-sw").getImmediate()}function Dr(t,e){return t=Je(t),kr(t,e)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */Tr();try{self["workbox:core:7.2.0"]&&_()}catch{}const Or=(t,...e)=>{let n=t;return e.length>0&&(n+=` :: ${JSON.stringify(e)}`),n},Mr=Or;class f extends Error{constructor(e,n){const s=Mr(e,n);super(s),this.name=e,this.details=n}}const Nr=new Set,m={googleAnalytics:"googleAnalytics",precache:"precache-v2",prefix:"workbox",runtime:"runtime",suffix:typeof registration<"u"?registration.scope:""},se=t=>[m.prefix,t,m.suffix].filter(e=>e&&e.length>0).join("-"),Pr=t=>{for(const e of Object.keys(m))t(e)},Ee={updateDetails:t=>{Pr(e=>{typeof t[e]=="string"&&(m[e]=t[e])})},getGoogleAnalyticsName:t=>t||se(m.googleAnalytics),getPrecacheName:t=>t||se(m.precache),getPrefix:()=>m.prefix,getRuntimeName:t=>t||se(m.runtime),getSuffix:()=>m.suffix};function je(t,e){const n=new URL(t);for(const s of e)n.searchParams.delete(s);return n.href}async function Lr(t,e,n,s){const r=je(e.url,n);if(e.url===r)return t.match(e,s);const i=Object.assign(Object.assign({},s),{ignoreSearch:!0}),a=await t.keys(e,i);for(const o of a){const c=je(o.url,n);if(r===c)return t.match(o,s)}}let O;function Br(){if(O===void 0){const t=new Response("");if("body"in t)try{new Response(t.body),O=!0}catch{O=!1}O=!1}return O}class $r{constructor(){this.promise=new Promise((e,n)=>{this.resolve=e,this.reject=n})}}async function Ur(){for(const t of Nr)await t()}const xr=t=>new URL(String(t),location.href).href.replace(new RegExp(`^${location.origin}`),"");function Fr(t){return new Promise(e=>setTimeout(e,t))}function He(t,e){const n=e();return t.waitUntil(n),n}async function jr(t,e){let n=null;if(t.url&&(n=new URL(t.url).origin),n!==self.location.origin)throw new f("cross-origin-copy-response",{origin:n});const s=t.clone(),i={headers:new Headers(s.headers),status:s.status,statusText:s.statusText},a=Br()?s.body:await s.blob();return new Response(a,i)}function Hr(){self.addEventListener("activate",()=>self.clients.claim())}try{self["workbox:precaching:7.2.0"]&&_()}catch{}const Kr="__WB_REVISION__";function Vr(t){if(!t)throw new f("add-to-cache-list-unexpected-type",{entry:t});if(typeof t=="string"){const i=new URL(t,location.href);return{cacheKey:i.href,url:i.href}}const{revision:e,url:n}=t;if(!n)throw new f("add-to-cache-list-unexpected-type",{entry:t});if(!e){const i=new URL(n,location.href);return{cacheKey:i.href,url:i.href}}const s=new URL(n,location.href),r=new URL(n,location.href);return s.searchParams.set(Kr,e),{cacheKey:s.href,url:r.href}}class Wr{constructor(){this.updatedURLs=[],this.notUpdatedURLs=[],this.handlerWillStart=async({request:e,state:n})=>{n&&(n.originalRequest=e)},this.cachedResponseWillBeUsed=async({event:e,state:n,cachedResponse:s})=>{if(e.type==="install"&&n&&n.originalRequest&&n.originalRequest instanceof Request){const r=n.originalRequest.url;s?this.notUpdatedURLs.push(r):this.updatedURLs.push(r)}return s}}}class qr{constructor({precacheController:e}){this.cacheKeyWillBeUsed=async({request:n,params:s})=>{const r=s?.cacheKey||this._precacheController.getCacheKeyForURL(n.url);return r?new Request(r,{headers:n.headers}):n},this._precacheController=e}}try{self["workbox:strategies:7.2.0"]&&_()}catch{}function B(t){return typeof t=="string"?new Request(t):t}class zr{constructor(e,n){this._cacheKeys={},Object.assign(this,n),this.event=n.event,this._strategy=e,this._handlerDeferred=new $r,this._extendLifetimePromises=[],this._plugins=[...e.plugins],this._pluginStateMap=new Map;for(const s of this._plugins)this._pluginStateMap.set(s,{});this.event.waitUntil(this._handlerDeferred.promise)}async fetch(e){const{event:n}=this;let s=B(e);if(s.mode==="navigate"&&n instanceof FetchEvent&&n.preloadResponse){const a=await n.preloadResponse;if(a)return a}const r=this.hasCallback("fetchDidFail")?s.clone():null;try{for(const a of this.iterateCallbacks("requestWillFetch"))s=await a({request:s.clone(),event:n})}catch(a){if(a instanceof Error)throw new f("plugin-error-request-will-fetch",{thrownErrorMessage:a.message})}const i=s.clone();try{let a;a=await fetch(s,s.mode==="navigate"?void 0:this._strategy.fetchOptions);for(const o of this.iterateCallbacks("fetchDidSucceed"))a=await o({event:n,request:i,response:a});return a}catch(a){throw r&&await this.runCallbacks("fetchDidFail",{error:a,event:n,originalRequest:r.clone(),request:i.clone()}),a}}async fetchAndCachePut(e){const n=await this.fetch(e),s=n.clone();return this.waitUntil(this.cachePut(e,s)),n}async cacheMatch(e){const n=B(e);let s;const{cacheName:r,matchOptions:i}=this._strategy,a=await this.getCacheKey(n,"read"),o=Object.assign(Object.assign({},i),{cacheName:r});s=await caches.match(a,o);for(const c of this.iterateCallbacks("cachedResponseWillBeUsed"))s=await c({cacheName:r,matchOptions:i,cachedResponse:s,request:a,event:this.event})||void 0;return s}async cachePut(e,n){const s=B(e);await Fr(0);const r=await this.getCacheKey(s,"write");if(!n)throw new f("cache-put-with-no-response",{url:xr(r.url)});const i=await this._ensureResponseSafeToCache(n);if(!i)return!1;const{cacheName:a,matchOptions:o}=this._strategy,c=await self.caches.open(a),l=this.hasCallback("cacheDidUpdate"),u=l?await Lr(c,r.clone(),["__WB_REVISION__"],o):null;try{await c.put(r,l?i.clone():i)}catch(d){if(d instanceof Error)throw d.name==="QuotaExceededError"&&await Ur(),d}for(const d of this.iterateCallbacks("cacheDidUpdate"))await d({cacheName:a,oldResponse:u,newResponse:i.clone(),request:r,event:this.event});return!0}async getCacheKey(e,n){const s=`${e.url} | ${n}`;if(!this._cacheKeys[s]){let r=e;for(const i of this.iterateCallbacks("cacheKeyWillBeUsed"))r=B(await i({mode:n,request:r,event:this.event,params:this.params}));this._cacheKeys[s]=r}return this._cacheKeys[s]}hasCallback(e){for(const n of this._strategy.plugins)if(e in n)return!0;return!1}async runCallbacks(e,n){for(const s of this.iterateCallbacks(e))await s(n)}*iterateCallbacks(e){for(const n of this._strategy.plugins)if(typeof n[e]=="function"){const s=this._pluginStateMap.get(n);yield i=>{const a=Object.assign(Object.assign({},i),{state:s});return n[e](a)}}}waitUntil(e){return this._extendLifetimePromises.push(e),e}async doneWaiting(){let e;for(;e=this._extendLifetimePromises.shift();)await e}destroy(){this._handlerDeferred.resolve(null)}async _ensureResponseSafeToCache(e){let n=e,s=!1;for(const r of this.iterateCallbacks("cacheWillUpdate"))if(n=await r({request:this.request,response:n,event:this.event})||void 0,s=!0,!n)break;return s||n&&n.status!==200&&(n=void 0),n}}class Gr{constructor(e={}){this.cacheName=Ee.getRuntimeName(e.cacheName),this.plugins=e.plugins||[],this.fetchOptions=e.fetchOptions,this.matchOptions=e.matchOptions}handle(e){const[n]=this.handleAll(e);return n}handleAll(e){e instanceof FetchEvent&&(e={event:e,request:e.request});const n=e.event,s=typeof e.request=="string"?new Request(e.request):e.request,r="params"in e?e.params:void 0,i=new zr(this,{event:n,request:s,params:r}),a=this._getResponse(i,s,n),o=this._awaitComplete(a,i,s,n);return[a,o]}async _getResponse(e,n,s){await e.runCallbacks("handlerWillStart",{event:s,request:n});let r;try{if(r=await this._handle(n,e),!r||r.type==="error")throw new f("no-response",{url:n.url})}catch(i){if(i instanceof Error){for(const a of e.iterateCallbacks("handlerDidError"))if(r=await a({error:i,event:s,request:n}),r)break}if(!r)throw i}for(const i of e.iterateCallbacks("handlerWillRespond"))r=await i({event:s,request:n,response:r});return r}async _awaitComplete(e,n,s,r){let i,a;try{i=await e}catch{}try{await n.runCallbacks("handlerDidRespond",{event:r,request:s,response:i}),await n.doneWaiting()}catch(o){o instanceof Error&&(a=o)}if(await n.runCallbacks("handlerDidComplete",{event:r,request:s,response:i,error:a}),n.destroy(),a)throw a}}class y extends Gr{constructor(e={}){e.cacheName=Ee.getPrecacheName(e.cacheName),super(e),this._fallbackToNetwork=e.fallbackToNetwork!==!1,this.plugins.push(y.copyRedirectedCacheableResponsesPlugin)}async _handle(e,n){const s=await n.cacheMatch(e);return s||(n.event&&n.event.type==="install"?await this._handleInstall(e,n):await this._handleFetch(e,n))}async _handleFetch(e,n){let s;const r=n.params||{};if(this._fallbackToNetwork){const i=r.integrity,a=e.integrity,o=!a||a===i;s=await n.fetch(new Request(e,{integrity:e.mode!=="no-cors"?a||i:void 0})),i&&o&&e.mode!=="no-cors"&&(this._useDefaultCacheabilityPluginIfNeeded(),await n.cachePut(e,s.clone()))}else throw new f("missing-precache-entry",{cacheName:this.cacheName,url:e.url});return s}async _handleInstall(e,n){this._useDefaultCacheabilityPluginIfNeeded();const s=await n.fetch(e);if(!await n.cachePut(e,s.clone()))throw new f("bad-precaching-response",{url:e.url,status:s.status});return s}_useDefaultCacheabilityPluginIfNeeded(){let e=null,n=0;for(const[s,r]of this.plugins.entries())r!==y.copyRedirectedCacheableResponsesPlugin&&(r===y.defaultPrecacheCacheabilityPlugin&&(e=s),r.cacheWillUpdate&&n++);n===0?this.plugins.push(y.defaultPrecacheCacheabilityPlugin):n>1&&e!==null&&this.plugins.splice(e,1)}}y.defaultPrecacheCacheabilityPlugin={async cacheWillUpdate({response:t}){return!t||t.status>=400?null:t}};y.copyRedirectedCacheableResponsesPlugin={async cacheWillUpdate({response:t}){return t.redirected?await jr(t):t}};class Jr{constructor({cacheName:e,plugins:n=[],fallbackToNetwork:s=!0}={}){this._urlsToCacheKeys=new Map,this._urlsToCacheModes=new Map,this._cacheKeysToIntegrities=new Map,this._strategy=new y({cacheName:Ee.getPrecacheName(e),plugins:[...n,new qr({precacheController:this})],fallbackToNetwork:s}),this.install=this.install.bind(this),this.activate=this.activate.bind(this)}get strategy(){return this._strategy}precache(e){this.addToCacheList(e),this._installAndActiveListenersAdded||(self.addEventListener("install",this.install),self.addEventListener("activate",this.activate),this._installAndActiveListenersAdded=!0)}addToCacheList(e){const n=[];for(const s of e){typeof s=="string"?n.push(s):s&&s.revision===void 0&&n.push(s.url);const{cacheKey:r,url:i}=Vr(s),a=typeof s!="string"&&s.revision?"reload":"default";if(this._urlsToCacheKeys.has(i)&&this._urlsToCacheKeys.get(i)!==r)throw new f("add-to-cache-list-conflicting-entries",{firstEntry:this._urlsToCacheKeys.get(i),secondEntry:r});if(typeof s!="string"&&s.integrity){if(this._cacheKeysToIntegrities.has(r)&&this._cacheKeysToIntegrities.get(r)!==s.integrity)throw new f("add-to-cache-list-conflicting-integrities",{url:i});this._cacheKeysToIntegrities.set(r,s.integrity)}if(this._urlsToCacheKeys.set(i,r),this._urlsToCacheModes.set(i,a),n.length>0){const o=`Workbox is precaching URLs without revision info: ${n.join(", ")}
This is generally NOT safe. Learn more at https://bit.ly/wb-precache`;console.warn(o)}}}install(e){return He(e,async()=>{const n=new Wr;this.strategy.plugins.push(n);for(const[i,a]of this._urlsToCacheKeys){const o=this._cacheKeysToIntegrities.get(a),c=this._urlsToCacheModes.get(i),l=new Request(i,{integrity:o,cache:c,credentials:"same-origin"});await Promise.all(this.strategy.handleAll({params:{cacheKey:a},request:l,event:e}))}const{updatedURLs:s,notUpdatedURLs:r}=n;return{updatedURLs:s,notUpdatedURLs:r}})}activate(e){return He(e,async()=>{const n=await self.caches.open(this.strategy.cacheName),s=await n.keys(),r=new Set(this._urlsToCacheKeys.values()),i=[];for(const a of s)r.has(a.url)||(await n.delete(a),i.push(a.url));return{deletedURLs:i}})}getURLsToCacheKeys(){return this._urlsToCacheKeys}getCachedURLs(){return[...this._urlsToCacheKeys.keys()]}getCacheKeyForURL(e){const n=new URL(e,location.href);return this._urlsToCacheKeys.get(n.href)}getIntegrityForCacheKey(e){return this._cacheKeysToIntegrities.get(e)}async matchPrecache(e){const n=e instanceof Request?e.url:e,s=this.getCacheKeyForURL(n);if(s)return(await self.caches.open(this.strategy.cacheName)).match(s)}createHandlerBoundToURL(e){const n=this.getCacheKeyForURL(e);if(!n)throw new f("non-precached-url",{url:e});return s=>(s.request=new Request(e),s.params=Object.assign({cacheKey:n},s.params),this.strategy.handle(s))}}let re;const Et=()=>(re||(re=new Jr),re);try{self["workbox:routing:7.2.0"]&&_()}catch{}const St="GET",H=t=>t&&typeof t=="object"?t:{handle:t};class N{constructor(e,n,s=St){this.handler=H(n),this.match=e,this.method=s}setCatchHandler(e){this.catchHandler=H(e)}}class Yr extends N{constructor(e,n,s){const r=({url:i})=>{const a=e.exec(i.href);if(a&&!(i.origin!==location.origin&&a.index!==0))return a.slice(1)};super(r,n,s)}}class Qr{constructor(){this._routes=new Map,this._defaultHandlerMap=new Map}get routes(){return this._routes}addFetchListener(){self.addEventListener("fetch",e=>{const{request:n}=e,s=this.handleRequest({request:n,event:e});s&&e.respondWith(s)})}addCacheListener(){self.addEventListener("message",e=>{if(e.data&&e.data.type==="CACHE_URLS"){const{payload:n}=e.data,s=Promise.all(n.urlsToCache.map(r=>{typeof r=="string"&&(r=[r]);const i=new Request(...r);return this.handleRequest({request:i,event:e})}));e.waitUntil(s),e.ports&&e.ports[0]&&s.then(()=>e.ports[0].postMessage(!0))}})}handleRequest({request:e,event:n}){const s=new URL(e.url,location.href);if(!s.protocol.startsWith("http"))return;const r=s.origin===location.origin,{params:i,route:a}=this.findMatchingRoute({event:n,request:e,sameOrigin:r,url:s});let o=a&&a.handler;const c=e.method;if(!o&&this._defaultHandlerMap.has(c)&&(o=this._defaultHandlerMap.get(c)),!o)return;let l;try{l=o.handle({url:s,request:e,event:n,params:i})}catch(d){l=Promise.reject(d)}const u=a&&a.catchHandler;return l instanceof Promise&&(this._catchHandler||u)&&(l=l.catch(async d=>{if(u)try{return await u.handle({url:s,request:e,event:n,params:i})}catch(v){v instanceof Error&&(d=v)}if(this._catchHandler)return this._catchHandler.handle({url:s,request:e,event:n});throw d})),l}findMatchingRoute({url:e,sameOrigin:n,request:s,event:r}){const i=this._routes.get(s.method)||[];for(const a of i){let o;const c=a.match({url:e,sameOrigin:n,request:s,event:r});if(c)return o=c,(Array.isArray(o)&&o.length===0||c.constructor===Object&&Object.keys(c).length===0||typeof c=="boolean")&&(o=void 0),{route:a,params:o}}return{}}setDefaultHandler(e,n=St){this._defaultHandlerMap.set(n,H(e))}setCatchHandler(e){this._catchHandler=H(e)}registerRoute(e){this._routes.has(e.method)||this._routes.set(e.method,[]),this._routes.get(e.method).push(e)}unregisterRoute(e){if(!this._routes.has(e.method))throw new f("unregister-route-but-not-found-with-method",{method:e.method});const n=this._routes.get(e.method).indexOf(e);if(n>-1)this._routes.get(e.method).splice(n,1);else throw new f("unregister-route-route-not-registered")}}let M;const Xr=()=>(M||(M=new Qr,M.addFetchListener(),M.addCacheListener()),M);function Zr(t,e,n){let s;if(typeof t=="string"){const i=new URL(t,location.href),a=({url:o})=>o.href===i.href;s=new N(a,e,n)}else if(t instanceof RegExp)s=new Yr(t,e,n);else if(typeof t=="function")s=new N(t,e,n);else if(t instanceof N)s=t;else throw new f("unsupported-route-type",{moduleName:"workbox-routing",funcName:"registerRoute",paramName:"capture"});return Xr().registerRoute(s),s}function ei(t,e=[]){for(const n of[...t.searchParams.keys()])e.some(s=>s.test(n))&&t.searchParams.delete(n);return t}function*ti(t,{ignoreURLParametersMatching:e=[/^utm_/,/^fbclid$/],directoryIndex:n="index.html",cleanURLs:s=!0,urlManipulation:r}={}){const i=new URL(t,location.href);i.hash="",yield i.href;const a=ei(i,e);if(yield a.href,n&&a.pathname.endsWith("/")){const o=new URL(a.href);o.pathname+=n,yield o.href}if(s){const o=new URL(a.href);o.pathname+=".html",yield o.href}if(r){const o=r({url:i});for(const c of o)yield c.href}}class ni extends N{constructor(e,n){const s=({request:r})=>{const i=e.getURLsToCacheKeys();for(const a of ti(r.url,n)){const o=i.get(a);if(o){const c=e.getIntegrityForCacheKey(o);return{cacheKey:o,integrity:c}}}};super(s,e.strategy)}}function si(t){const e=Et(),n=new ni(e,t);Zr(n)}function ri(t){Et().precache(t)}function ii(t,e){ri(t),si(e)}ii([{"revision":"400f90d6f5f42eeef7d9df0f1bac6c82","url":"apple-touch-icon.png"},{"revision":null,"url":"assets/Abilities-JbC2Pyb2.js"},{"revision":null,"url":"assets/abilities-OclsAqoB.png"},{"revision":null,"url":"assets/admin-CxPP_qbb.png"},{"revision":null,"url":"assets/adventure-Bp-cQoF0.png"},{"revision":null,"url":"assets/AdventurePage-d3w9x405.js"},{"revision":null,"url":"assets/ag-theme-quartz-41SzhtwZ.js"},{"revision":null,"url":"assets/ag-theme-quartz-BKv5Qpmd.css"},{"revision":null,"url":"assets/angry-DYivJtBQ.png"},{"revision":null,"url":"assets/APicon3-CTiyfJlQ.png"},{"revision":null,"url":"assets/arcade-BIkkTRDG.png"},{"revision":null,"url":"assets/Arcade-C40UJAeu.js"},{"revision":null,"url":"assets/arrow-left-DyjAfoBb.js"},{"revision":null,"url":"assets/arrow-uJJzj90d.gif"},{"revision":null,"url":"assets/attack-BLmMBc-T.png"},{"revision":null,"url":"assets/backButton-DvB-fftI.png"},{"revision":null,"url":"assets/backgroundImg-3EK4Ifxm.webp"},{"revision":null,"url":"assets/backgroundImg-BZ7IgI6U.webp"},{"revision":null,"url":"assets/backgroundImg-C8THYgdB.webp"},{"revision":null,"url":"assets/backgroundImg-CaBgDC45.webp"},{"revision":null,"url":"assets/backgroundImg-cxMMHQS6.webp"},{"revision":null,"url":"assets/backgroundImg-CxrQswXt.webp"},{"revision":null,"url":"assets/baka-D-4TckS2.png"},{"revision":null,"url":"assets/Bank-Cep_KE5X.js"},{"revision":null,"url":"assets/banknote-CP9y1DAo.js"},{"revision":null,"url":"assets/berserker-CcNKNwk6.png"},{"revision":null,"url":"assets/blue3-Bkwl1QpL.jpg"},{"revision":null,"url":"assets/blueBars-X0WSUevI.jpg"},{"revision":null,"url":"assets/blueframe-DYlU065E.png"},{"revision":null,"url":"assets/blueSliderFill-CJOtkgQV.png"},{"revision":null,"url":"assets/bodyRegen-CUoc4dCz.png"},{"revision":null,"url":"assets/book-open-CWI-FDJw.js"},{"revision":null,"url":"assets/BountyBoard-B4-LrOg0.js"},{"revision":null,"url":"assets/brain-BhECYlGz.js"},{"revision":null,"url":"assets/built-Ekk7e7JB.png"},{"revision":null,"url":"assets/bully-BrIGeiHK.png"},{"revision":null,"url":"assets/bunny-DgsWqt8r.png"},{"revision":null,"url":"assets/cafe-Czyfu_EL.webp"},{"revision":null,"url":"assets/Calendar-By5_rAJY.js"},{"revision":null,"url":"assets/calendar-DumqBxLG.js"},{"revision":null,"url":"assets/Callout-CiwZA3Lq.js"},{"revision":null,"url":"assets/cartoonPattern-oMTjJ3v0.png"},{"revision":null,"url":"assets/cartoonPatternFull-DWt-CNTS.png"},{"revision":null,"url":"assets/Casino-DCMig-D4.js"},{"revision":null,"url":"assets/casino-esO0707x.webp"},{"revision":null,"url":"assets/catjump-CPyW3lbX.gif"},{"revision":null,"url":"assets/chaining-DG-sm0UC.png"},{"revision":null,"url":"assets/Character-9KY1ejmm.js"},{"revision":null,"url":"assets/character-ClxC3w3i.png"},{"revision":null,"url":"assets/chat-DISOVqRS.png"},{"revision":null,"url":"assets/chatMsg-CW79yXce.mp3"},{"revision":null,"url":"assets/chevron-up-CjBLp6J4.js"},{"revision":null,"url":"assets/christmas-DEu9Iv_I.png"},{"revision":null,"url":"assets/circle-alert-DRw32lYi.js"},{"revision":null,"url":"assets/circle-check-big-VNVfdZu3.js"},{"revision":null,"url":"assets/circle-question-mark-CfYC5tXM.js"},{"revision":null,"url":"assets/circle-x-DEoYnmxi.js"},{"revision":null,"url":"assets/classroom-CLhUWM0N.webp"},{"revision":null,"url":"assets/classroom1Day-BF35Yh1x.webp"},{"revision":null,"url":"assets/claw-B8W427Tp.png"},{"revision":null,"url":"assets/combatRegen-BiCxXyaJ.png"},{"revision":null,"url":"assets/construction-DabPkmp4.webp"},{"revision":null,"url":"assets/consumables-lUwNcz6B.png"},{"revision":null,"url":"assets/continue-BIFtnR7n.png"},{"revision":null,"url":"assets/Courses-CG9tq963.js"},{"revision":null,"url":"assets/coward-Da6VeiGp.png"},{"revision":null,"url":"assets/craft-ByVwmzC4.png"},{"revision":null,"url":"assets/CraftingWorkshopPage-BSNyMGBX.js"},{"revision":null,"url":"assets/cripple-wLssv--F.png"},{"revision":null,"url":"assets/crippled-R8ZmHpKe.png"},{"revision":null,"url":"assets/cry-Xox1O2WK.png"},{"revision":null,"url":"assets/culture-BV_CsUBG.png"},{"revision":null,"url":"assets/cunning-DV04IzD5.png"},{"revision":null,"url":"assets/currency2-Bpjz_OrF.png"},{"revision":null,"url":"assets/dailychest-OOUVtqsz.png"},{"revision":null,"url":"assets/DailyTaskPage-BIo-qo0N.js"},{"revision":null,"url":"assets/darkBlueButtonBG-CtVbXbmJ.svg"},{"revision":null,"url":"assets/darkBlueButtonBG-DlkDGQfX.png"},{"revision":null,"url":"assets/DataTable-Dq0uy33X.js"},{"revision":null,"url":"assets/defaultAvatar-DdtHYP0A.png"},{"revision":null,"url":"assets/defence-MpD812P6.png"},{"revision":null,"url":"assets/deflect--BTIcwGf.png"},{"revision":null,"url":"assets/dexterity-6ahwAW6O.png"},{"revision":null,"url":"assets/dies-DI2d-WMs.gif"},{"revision":null,"url":"assets/differenceInHours-CqXSCWk0.js"},{"revision":null,"url":"assets/disarm-D9y5Gj7F.png"},{"revision":null,"url":"assets/disarmed-PHcQYjWn.png"},{"revision":null,"url":"assets/disclosure-BUfTMVck.js"},{"revision":null,"url":"assets/Discord-DaDKms4T.js"},{"revision":null,"url":"assets/dumbbell-BjL3e2l6.js"},{"revision":null,"url":"assets/emo8-GiwerJ1C.png"},{"revision":null,"url":"assets/emojiButton-BXHSeT55.png"},{"revision":null,"url":"assets/energetic-ls-tXnMT.png"},{"revision":null,"url":"assets/energyicon-BwINCWoA.png"},{"revision":null,"url":"assets/enhanced-BNSBM5nP.png"},{"revision":null,"url":"assets/enraged-84ZbFSNp.png"},{"revision":null,"url":"assets/escapeArtist-BJ99H1_1.png"},{"revision":null,"url":"assets/events-BkXSD3Ep.png"},{"revision":null,"url":"assets/Events-oXfw9NPJ.js"},{"revision":null,"url":"assets/exhaust-DCKeUgI1.png"},{"revision":null,"url":"assets/exhausted-Cr1rRUFR.png"},{"revision":null,"url":"assets/expBG-BGDsopHP.png"},{"revision":null,"url":"assets/expicon-BeF7u3m_.png"},{"revision":null,"url":"assets/explore-BuhS9zcc.png"},{"revision":null,"url":"assets/ExplorePage-DabJNqZE.js"},{"revision":null,"url":"assets/expSliderBG-CnhHBKjT.png"},{"revision":null,"url":"assets/expSliderFill-BhwbzH6r.png"},{"revision":null,"url":"assets/FacultyList-CYxKNMu7.js"},{"revision":null,"url":"assets/fetch-DHMFazHH.png"},{"revision":null,"url":"assets/flee-R9tikEwx.png"},{"revision":null,"url":"assets/freeMovement-BisXPF0c.png"},{"revision":null,"url":"assets/frenzied-5CbmBTWG.png"},{"revision":null,"url":"assets/FullChat-tB2QuHzc.js"},{"revision":null,"url":"assets/gang-CIz4GXEf.png"},{"revision":null,"url":"assets/Gang-mV1k_sl8.js"},{"revision":null,"url":"assets/GangLeaderboards-DtDhDNue.js"},{"revision":null,"url":"assets/GangList-yGGzvf9X.js"},{"revision":null,"url":"assets/getJobImage-xd_CqHBk.js"},{"revision":null,"url":"assets/getUserItemCount-CIJt4adY.js"},{"revision":null,"url":"assets/grayButtonBG-BU03f1GU.svg"},{"revision":null,"url":"assets/grayButtonBG-qEuiB7EG.png"},{"revision":null,"url":"assets/green1-Dj5gmr5Y.jpg"},{"revision":null,"url":"assets/greenframe-CbRYt0fX.png"},{"revision":null,"url":"assets/greenSliderFill-CYQBp405.png"},{"revision":null,"url":"assets/greenTick-CRl6ZXFn.js"},{"revision":null,"url":"assets/guarding-LiwX2fx2.png"},{"revision":null,"url":"assets/hallway1Day-BuLLK_P1.webp"},{"revision":null,"url":"assets/hammer-ChN3i2E9.js"},{"revision":null,"url":"assets/happy-D53xDMqa.webp"},{"revision":null,"url":"assets/happyopen-BjJ5ngIm.webp"},{"revision":null,"url":"assets/happyopen-BywJr-Fd.webp"},{"revision":null,"url":"assets/happyopen-CRvy9v7Z.webp"},{"revision":null,"url":"assets/happyopenShadow-mtmPWIpd.webp"},{"revision":null,"url":"assets/happyopenSmall-CfbW0upK.webp"},{"revision":null,"url":"assets/happyopenSmall-Cz3BU7F2.webp"},{"revision":null,"url":"assets/happyopenSmall-DbUUICuH.webp"},{"revision":null,"url":"assets/happySmall-C42ob1BY.webp"},{"revision":null,"url":"assets/headbutt-B1_YV36I.png"},{"revision":null,"url":"assets/headmaster_suit_angry-CTLLzgFC.webp"},{"revision":null,"url":"assets/heal-fRmk4vQ3.png"},{"revision":null,"url":"assets/healovertime-edkXbaAD.png"},{"revision":null,"url":"assets/healthyCaster-eX1umIhb.png"},{"revision":null,"url":"assets/heh-txDCJ7q0.png"},{"revision":null,"url":"assets/highguard-9KUfg1bT.png"},{"revision":null,"url":"assets/hii-D7S-nf01.gif"},{"revision":null,"url":"assets/hmm-C5GjdB-J.png"},{"revision":null,"url":"assets/Hospital-CrJdSc8n.js"},{"revision":null,"url":"assets/hospital-yQ8YbERO.png"},{"revision":null,"url":"assets/HPicon-D4GSC55Z.png"},{"revision":null,"url":"assets/HPicon2-BpgU42R7.png"},{"revision":null,"url":"assets/hyped-BXWhD6Xn.gif"},{"revision":null,"url":"assets/hyperjump-BxOnrsHK.gif"},{"revision":null,"url":"assets/Icon_ImageIcon_Medal_Bronze-MKBcOfF8.png"},{"revision":null,"url":"assets/Icon_ImageIcon_Medal_Gold-T6T-L8Ga.png"},{"revision":null,"url":"assets/Icon_ImageIcon_Medal_Silver-CEIwxd4F.png"},{"revision":null,"url":"assets/Icon_ImageIcon_Medal_Silver-OH_4ykaO.js"},{"revision":null,"url":"assets/Inbox-BUqfBcvq.css"},{"revision":null,"url":"assets/Inbox-D97r9WwY.js"},{"revision":null,"url":"assets/index-CAuTn8O8.css"},{"revision":null,"url":"assets/index-DC0BXKDb.js"},{"revision":null,"url":"assets/index-KoAI3AHM.js"},{"revision":null,"url":"assets/intelligence-BrHC6g6d.png"},{"revision":null,"url":"assets/intervalToDuration-CAYv-tqh.js"},{"revision":null,"url":"assets/Inventory-Ci96Qp3x.css"},{"revision":null,"url":"assets/Inventory-DIV941Ny.js"},{"revision":null,"url":"assets/inventory-Dz1d2pHE.png"},{"revision":null,"url":"assets/investor-Dvl8Hu30.png"},{"revision":null,"url":"assets/Jail-BmAv4ap9.js"},{"revision":null,"url":"assets/jail-DSmMbh5p.png"},{"revision":null,"url":"assets/jailbars-BvzW8JvC.png"},{"revision":null,"url":"assets/jobs-BDLp4JM9.webp"},{"revision":null,"url":"assets/jump-Bl2WOmC2.gif"},{"revision":null,"url":"assets/kalm-BZb22g9v.png"},{"revision":null,"url":"assets/kappa-DoK2B1Jz.png"},{"revision":null,"url":"assets/LatestNews-DjU45x7G.js"},{"revision":null,"url":"assets/leaderboard-D6o9iRcx.webp"},{"revision":null,"url":"assets/Leaderboard-Dc6Qad83.js"},{"revision":null,"url":"assets/leaderboards-CdqvSwhr.png"},{"revision":null,"url":"assets/learner-BVuwyufs.png"},{"revision":null,"url":"assets/leftarrow-DZyJmGgR.png"},{"revision":null,"url":"assets/legendary-DOZ_Skqf.png"},{"revision":null,"url":"assets/levelBadge-uEikayln.png"},{"revision":null,"url":"assets/levelup-CB5E89to.png"},{"revision":null,"url":"assets/lexend-C4kThqOm.woff2"},{"revision":null,"url":"assets/lifeEssence-BsbKaMCd.png"},{"revision":null,"url":"assets/light-BHrEBvor.png"},{"revision":null,"url":"assets/lmao-Dan7iibn.gif"},{"revision":null,"url":"assets/locked-ly7i4j0n.png"},{"revision":null,"url":"assets/lockedchest-Db25p4X2.png"},{"revision":null,"url":"assets/logoblack-B1rLzQXN.png"},{"revision":null,"url":"assets/logoCharacter-CKZKBhoy.webp"},{"revision":null,"url":"assets/logoTextSVG-D4njN0cX.svg"},{"revision":null,"url":"assets/map-pin-BCfkkrI7.js"},{"revision":null,"url":"assets/Market-mN57xSDz.js"},{"revision":null,"url":"assets/melee-DzjqxZF9.png"},{"revision":null,"url":"assets/meleeDmg-DocnD4s9.png"},{"revision":null,"url":"assets/menu-uucggh9W.js"},{"revision":null,"url":"assets/messages-D_U0ynyw.png"},{"revision":null,"url":"assets/metabolism-Bb1bhdi5.png"},{"revision":null,"url":"assets/military-Br_mmXDW.png"},{"revision":null,"url":"assets/missingIcon-BZT45_3d.png"},{"revision":null,"url":"assets/Missions-D7Dhwf4S.js"},{"revision":null,"url":"assets/mitigation-Bcff0MX6.png"},{"revision":null,"url":"assets/mobileNavBG-DVDFipyq.png"},{"revision":null,"url":"assets/mobileNavBGDark-CB0uG0GA.png"},{"revision":null,"url":"assets/mugger-DyLN16EX.png"},{"revision":null,"url":"assets/multitasker-D8QqoWZR.png"},{"revision":null,"url":"assets/NetworkError-CsPzDWii.png"},{"revision":null,"url":"assets/neutral-CKcwP3Gg.webp"},{"revision":null,"url":"assets/neutral-DydpajS9.webp"},{"revision":null,"url":"assets/neutralShadow-CTW1ix-z.webp"},{"revision":null,"url":"assets/neutralShadow-DV9lvAE8.webp"},{"revision":null,"url":"assets/neutralSmall-C7dyofyr.webp"},{"revision":null,"url":"assets/neutralSmall-CiDumcaB.webp"},{"revision":null,"url":"assets/news-CZbTg9Bq.png"},{"revision":null,"url":"assets/no-BAs6ATi3.gif"},{"revision":null,"url":"assets/nobully-hddR5pUr.png"},{"revision":null,"url":"assets/novice-5eh4GiAI.png"},{"revision":null,"url":"assets/npc_kill-V8wRFwoS.png"},{"revision":null,"url":"assets/offhands-D1CAKh_r.png"},{"revision":null,"url":"assets/office-CE9JU_6X.webp"},{"revision":null,"url":"assets/orangeBars-C9x1tbO3.jpg"},{"revision":null,"url":"assets/panik-BipToAHb.png"},{"revision":null,"url":"assets/PartTimeJobListings-BoHWhAI2.js"},{"revision":null,"url":"assets/patchNotes-CWZ9Xpkl.js"},{"revision":null,"url":"assets/PetsPage-Dzof49Qs.js"},{"revision":null,"url":"assets/pink2-CjLFDBrJ.jpg"},{"revision":null,"url":"assets/poisoned-8J8o479p.png"},{"revision":null,"url":"assets/Polls-Bx2MlljU.css"},{"revision":null,"url":"assets/Polls-COjguIUh.js"},{"revision":null,"url":"assets/popcat-N8Tq8q_D.gif"},{"revision":null,"url":"assets/processing-ClkX9LpC.png"},{"revision":null,"url":"assets/PropertyPage-diN1VIlC.js"},{"revision":null,"url":"assets/purple2-DH370tFu.jpg"},{"revision":null,"url":"assets/pvp_kill-sG7FJmrZ.png"},{"revision":null,"url":"assets/quiver-CEWXcocX.png"},{"revision":null,"url":"assets/rage-CxQ1PUH2.png"},{"revision":null,"url":"assets/ramen-BWFMw-vw.webp"},{"revision":null,"url":"assets/ranged-DeiLteUu.png"},{"revision":null,"url":"assets/ranger-T0q4gbQT.png"},{"revision":null,"url":"assets/rarityColours-eyuwwjU9.js"},{"revision":null,"url":"assets/rawMaterials-BGJJK4mz.png"},{"revision":null,"url":"assets/recovery-CVUd61zL.png"},{"revision":null,"url":"assets/Referrals-DYIjZeE4.js"},{"revision":null,"url":"assets/rejuvenation-Bho8_TBt.png"},{"revision":null,"url":"assets/reload-CcVyUsaa.png"},{"revision":null,"url":"assets/respect-C47rUSm7.png"},{"revision":null,"url":"assets/ribbonPurple-Cto_nvlv.png"},{"revision":null,"url":"assets/ribbonRed-DuSpEbKG.png"},{"revision":null,"url":"assets/rightarrow-DmLB-4mF.png"},{"revision":null,"url":"assets/rigidity-D0891Q6Y.png"},{"revision":null,"url":"assets/Rooftop-CTPVi90a.js"},{"revision":null,"url":"assets/sad-DYUjJwXW.png"},{"revision":null,"url":"assets/school-CYIItOoz.png"},{"revision":null,"url":"assets/School-DyWipCBi.js"},{"revision":null,"url":"assets/search-Cd4bdjpR.png"},{"revision":null,"url":"assets/secondWind-IOUYxsOR.png"},{"revision":null,"url":"assets/selfHarm-Bq4uGDZ1.png"},{"revision":null,"url":"assets/sendButton-DVsVPC1S.png"},{"revision":null,"url":"assets/Settings-Bs3gl52x.css"},{"revision":null,"url":"assets/Settings-BxsP4h3I.js"},{"revision":null,"url":"assets/settings-ClflpW0-.js"},{"revision":null,"url":"assets/settings-Du5Rj-be.png"},{"revision":null,"url":"assets/shake-B93L_x42.webp"},{"revision":null,"url":"assets/shieldbash-VF4uy7B1.png"},{"revision":null,"url":"assets/shieldbearer-IM3Gy17m.png"},{"revision":null,"url":"assets/ShoeLocker-C9la3csq.js"},{"revision":null,"url":"assets/Shops-CEpAa3jn.js"},{"revision":null,"url":"assets/shopsicon-CxJUSSkR.webp"},{"revision":null,"url":"assets/Shrine-cTBTVdr0.js"},{"revision":null,"url":"assets/shrineicon-BvwFPa2C.webp"},{"revision":null,"url":"assets/shutup-CIkCyalS.png"},{"revision":null,"url":"assets/SingleShop-B4y6a3fy.js"},{"revision":null,"url":"assets/sip-rPpH3tVl.png"},{"revision":null,"url":"assets/skyblueButtonBG-DdxC0zVJ.svg"},{"revision":null,"url":"assets/sleep-BWM8PiPn.png"},{"revision":null,"url":"assets/sliderBG-D7hjpQfe.png"},{"revision":null,"url":"assets/slingshot-DeA7PlH1.png"},{"revision":null,"url":"assets/social-aHez_M5M.png"},{"revision":null,"url":"assets/special-BNSh1yjW.png"},{"revision":null,"url":"assets/specialist-C84bVtuc.png"},{"revision":null,"url":"assets/speedcrafter-BB_5-GZE.png"},{"revision":null,"url":"assets/speedster-BOG4FcYf.png"},{"revision":null,"url":"assets/sports-Cga6f8Rf.png"},{"revision":null,"url":"assets/spray-C3x2ETFS.png"},{"revision":null,"url":"assets/squareBtnBlueBG-K8IjueNg.png"},{"revision":null,"url":"assets/stamina-LF5EZP0M.js"},{"revision":null,"url":"assets/stamina-Ls_Rw_Ob.png"},{"revision":null,"url":"assets/staminaRegen-CqqxDf8f.png"},{"revision":null,"url":"assets/standard-C2pZDbde.png"},{"revision":null,"url":"assets/StreetsPage-ChG-6rmU.js"},{"revision":null,"url":"assets/strength-DYbaT-zp.png"},{"revision":null,"url":"assets/strongbones-C5aVrH6O.png"},{"revision":null,"url":"assets/stun-6R21gpv7.png"},{"revision":null,"url":"assets/stunned-CUkux3PO.png"},{"revision":null,"url":"assets/Suggestions-94C_s4Nv.js"},{"revision":null,"url":"assets/surprise-D-SpDHVQ.gif"},{"revision":null,"url":"assets/talentData-CZzZ7076.js"},{"revision":null,"url":"assets/talents-CMBdjcAt.png"},{"revision":null,"url":"assets/TalentsView-CK6X4zNF.js"},{"revision":null,"url":"assets/TalentTree-DkiCNWFW.js"},{"revision":null,"url":"assets/tasks-BUMSNglv.png"},{"revision":null,"url":"assets/tasksOld-Ba2_3B_A.png"},{"revision":null,"url":"assets/TasksPage-B_5CY_40.js"},{"revision":null,"url":"assets/terraformerlogo-ad7GCudx.png"},{"revision":null,"url":"assets/thinking-Dj7UOHWy.png"},{"revision":null,"url":"assets/tongue-CqKru_nm.gif"},{"revision":null,"url":"assets/toxicdart-BYLi4Rvq.png"},{"revision":null,"url":"assets/TraderRep-DWjXZ70I.js"},{"revision":null,"url":"assets/Training-CVG6qFDm.js"},{"revision":null,"url":"assets/trending-up-_qSUGfOX.js"},{"revision":null,"url":"assets/unique-CQIkJl13.png"},{"revision":null,"url":"assets/Updates-Dy3XEhOr.js"},{"revision":null,"url":"assets/useGetInventory-4cW0XzoG.js"},{"revision":null,"url":"assets/useGetQuestObjectiveText-DejZoSM0.js"},{"revision":null,"url":"assets/useGetUnlockedTalents-CUtwY_Cf.js"},{"revision":null,"url":"assets/useGetUserSkills---hRFviy.js"},{"revision":null,"url":"assets/UsersTable-sp_Z5OTe.js"},{"revision":null,"url":"assets/ViewGangModal-DOlBiXz3.js"},{"revision":null,"url":"assets/waa-DuIc44LN.png"},{"revision":null,"url":"assets/wallet-DmAu7-Dc.js"},{"revision":null,"url":"assets/welcome-Bhq4nRFN.png"},{"revision":null,"url":"assets/wink-7gY75siX.gif"},{"revision":null,"url":"assets/workbox-window.prod.es5-B9K5rw8f.js"},{"revision":null,"url":"assets/yellowSliderFill-zVKZ4IKA.png"},{"revision":null,"url":"assets/yen-CBeGBZoQ.png"},{"revision":null,"url":"assets/YourJob-D68eqdQ9.js"},{"revision":null,"url":"assets/yup-BS27Th5G.gif"},{"revision":"a493ba0aa0b8ec8068d786d7248bb92c","url":"browserconfig.xml"},{"revision":"8da8c06dafa28f4103c5ed7984428fc4","url":"favicon.ico"},{"revision":"51fdff3fad2db8d8a32d582958f05769","url":"faviconOld.ico"},{"revision":"a070caf89f50b2964cb216f0a9736c37","url":"index.html"},{"revision":"15401a96d38afe4e8fb210f9a1e689df","url":"logo192.png"},{"revision":"92db626bf25065ee41aed8c0ba60c2dc","url":"logo512.png"},{"revision":"0e41d6d14d436ab1f2ae731cde86d524","url":"manifest.webmanifest"},{"revision":"fa1ded1ed7c11438a9b0385b1e112850","url":"robots.txt"},{"revision":"576ce7e02bfe2f9073959a42ad92d481","url":"safari-pinned-tab.svg"},{"revision":"951705c5ef0c47eeacc5f9a3b71443b7","url":"service-worker.js"},{"revision":"15401a96d38afe4e8fb210f9a1e689df","url":"logo192.png"},{"revision":"92db626bf25065ee41aed8c0ba60c2dc","url":"logo512.png"},{"revision":"0e41d6d14d436ab1f2ae731cde86d524","url":"manifest.webmanifest"}]);const ai={apiKey:"AIzaSyCQ-QeWMrBZw8v9idybo_cFwAIlDO6j-z0",authDomain:"chikara-academy.firebaseapp.com",projectId:"chikara-academy",storageBucket:"chikara-academy.appspot.com",messagingSenderId:"75175802639",appId:"1:75175802639:web:a1cc5e6073f185ec46707a"},oi=Xe(ai),ci=Rr(oi);Dr(ci,t=>{console.log("[firebase-messaging-sw.js] Received background message",t)});self.skipWaiting();Hr();
