import{d as W,e as O,o as b,k as F,aj as he,j as e,ak as fe,g as R,ag as ge,al as G,am as U,C as z,b as H,an as h,S as ye,r as c,v as K,ad as J,y as w,ao as je,B as be,a5 as D,ap as X,a7 as we,t as Z,l as Ne,ae as ve,aq as Ie,N as Ee,ar as Ce,a as Se,as as Y}from"./index-DC0BXKDb.js";import{r as ee}from"./rarityColours-eyuwwjU9.js";import{D as ke}from"./DataTable-Dq0uy33X.js";import{u as Te}from"./useGetInventory-4cW0XzoG.js";import"./ag-theme-quartz-41SzhtwZ.js";const te=()=>{const r=W(),s=O(b.user.equipItem.mutationOptions({onMutate:async n=>{const i=n._userItem;await r.cancelQueries({queryKey:b.user.getEquippedItems.key()});const o=r.getQueryData(b.user.getEquippedItems.key());return r.setQueryData(b.user.getEquippedItems.key(),m=>({...{...m},[i.itemType]:i})),{previousEquippedItems:o}},onError:(n,i,o)=>{r.setQueryData(b.user.getEquippedItems.key(),o.previousEquippedItems),F.error(n.message||"An error occurred")},onSettled:async()=>{await r.invalidateQueries({queryKey:b.user.getEquippedItems.key()})},onSuccess:async()=>{await r.invalidateQueries({queryKey:b.user.getEquippedItems.key()}),F.success("Item equipped!")}}));return{equipItem:{...s,mutate:n=>{const{currentUser:i,userItem:o}=n;return i?.hospitalisedUntil>0?(F.error("Can't equip items while hospitalised!"),Promise.reject(new Error("Can't equip items while hospitalised!"))):i?.jailedUntil>0?(F.error("Can't equip items while jailed!"),Promise.reject(new Error("Can't equip items while jailed!"))):s.mutate({userItemId:o.id,_userItem:o})}}}},Re=({inventory:r,currentUser:s,equippedItems:t})=>{const{equipItem:n}=te(),{hideItemTooltip:i,setHideItemTooltip:o}=he(),m=a=>Object.entries(a).map(([x,g])=>{const y=(g-1)*100;return e.jsxs("p",{className:"text-base",children:[e.jsxs("span",{className:"text-custom-yellow",children:["+",Math.round(x==="health"?g:y)]}),x!=="health"&&"%"," ",x.replace("health","HP").replace("strength","STR").replace("dexterity","DEX").replace("intelligence","INT").replace("lifesteal","LIFE STEAL").replace("defence","DEF").replace("npcDamage","Increased DMG vs NPCs").replace("fleeChance","chance to Flee").replace("encounterReward","Yen gained from encounters")]},x)}),p=a=>{const x=t?.[a.itemType]||null;if(!x)return!1;if(a.id===x.id)return!0};return e.jsx(fe,{openOnClick:!0,clickable:!0,id:"equip-tooltip",afterHide:()=>i&&o(!1),className:"pointer-events-auto z-600 max-h-[40dvh] overflow-y-auto overflow-x-hidden border border-gray-600/50",opacity:"1",style:{backgroundColor:"rgba(7, 6, 7, 0.97)",color:"#FFF",padding:0},render:()=>e.jsxs("div",{className:"flex h-full w-72 flex-col py-2 text-center xl:w-80",children:[(!r||r.length===0)&&e.jsx("p",{className:"text-center text-sm",children:"No items available"}),r?.map((a,x)=>e.jsxs("div",{children:[e.jsx("hr",{className:R("my-1.5 border-gray-600/75",x===0?"hidden":"")}),e.jsxs("div",{className:"flex items-center justify-center gap-3 px-4",children:[e.jsx("div",{className:"w-[13%]",children:e.jsx("img",{className:R("z-10 mx-auto aspect-square max-w-10 grid-cols-2"),src:"https://cloudflare-image.jamessut.workers.dev/"+a.item.image,alt:"",onError:g=>g.target.src=ge(!0)})}),e.jsxs("div",{className:"flex flex-1 flex-col",children:[e.jsxs("p",{className:R(ee(a.item.rarity),"text-base"),children:[a.item.name," ",a.upgradeLevel>0&&e.jsxs("span",{children:["+",a.upgradeLevel]})]}),e.jsxs("div",{className:"flex min-w-24 flex-col text-gray-200 text-sm",children:[a.item.damage>0&&e.jsxs("div",{children:[e.jsx("span",{className:"mr-1 text-blue-500 text-lg",children:G(a,"damage")})," ","DMG",e.jsx(U,{item:a.item,equippedItems:t,type:"damage"})]}),a.item.armour>0&&e.jsxs("div",{className:"",children:[e.jsx("span",{className:"mr-1 text-blue-500 text-sm",children:G(a,"armour")})," ","ARMOR",e.jsx(U,{item:a.item,equippedItems:t,type:"armour"})]}),a.item.baseAmmo>0||a.item.strength>0||a.item.dexterity>0||a.item.statModifiers?e.jsxs(e.Fragment,{children:[a.item.baseAmmo>0&&e.jsxs("div",{className:"text-base",children:[a.item.baseAmmo," Base Ammo"," ",e.jsx(U,{item:a.item,equippedItems:t,type:"ammo"})]}),a.item.strength>0&&e.jsxs("div",{className:"text-base text-custom-yellow",children:["+",a.item.strength,"% STR"," ",e.jsx(U,{item:a.item,equippedItems:t,type:"strength"})]}),a.item.dexterity>0&&e.jsxs("div",{className:"text-base text-custom-yellow",children:["+",a.item.dexterity,"% DEX"," ",e.jsx(U,{item:a.item,equippedItems:t,type:"dexterity"})]}),a.item.statModifiers&&e.jsxs("div",{className:"mx-auto flex text-base text-custom-yellow",children:[m(a),e.jsx(U,{item:a.item,equippedItems:t,type:"modifiers"})]})]}):null]})]}),e.jsx("div",{className:"-mr-2 flex flex-1 justify-end md:mr-0",children:p(a.item)?e.jsx("span",{className:"mr-2 text-custom-yellow",children:"Equipped"}):e.jsx(z,{onClick:()=>n.mutate({currentUser:s,userItem:a}),children:"Equip"})})]})]},a.id))]})})},qe=(r={})=>H(b.item.getDailyChestItems.queryOptions({staleTime:3e5,...r}));class V{itemId;itemName;itemQuantity;rarity;itemImage;dropRate;constructor(s,t){this.itemId=s,this.itemName=t.itemName,this.itemQuantity=t.itemQuantity,this.rarity=t.rarity,this.itemImage=t.itemImage}}class Me{winner;allWeapons;rouletteWrapper;weaponWrapper;weaponsRef;weapons;weaponsCount;weaponPrizeId;transitionDuration;itemWidth;constructor(s){this.winner={itemName:"Placeholder",itemId:-1,itemQuantity:"",rarity:"",itemImage:"",dropRate:0},this.allWeapons=s.weapons,this.weapons=[],this.weaponsRef=s.weaponsRef,this.rouletteWrapper=s.weaponsRef,this.weaponWrapper=s.weaponsRef,this.weaponsCount=s.weaponsCount||50,this.weaponPrizeId=this.randomRange(this.weaponsCount/2,this.weaponsCount-5),this.transitionDuration=s.transitionDuration||10,this.itemWidth=s.itemWidth||100}randomRange=(s,t)=>Math.floor(Math.random()*(t-s+1))+s;shuffle=s=>{for(let t=s?.length-1;t>0;t--){const n=Math.floor(Math.random()*(t+1));[s[t],s[n]]=[s[n],s[t]]}};set_weapons=()=>{let s=[];const t=this.allWeapons?.length,n=(i,o)=>{let m=0;const p=[];for(let a=i;a<=o;a+=1)p.push(new V(a,this.allWeapons[m])),m=m===t-1?0:m+1;return this.shuffle(p),p};if(t===0)throw new Error("Ошибка! Нет актёров.");s=s.concat(n(0,this.weaponPrizeId-1)),s[this.weaponPrizeId]=new V(this.weaponPrizeId,this.winner),s=s.concat(n(this.weaponPrizeId+1,this.weaponsCount-1)),this.weapons=s};spin=()=>{const s=Math.floor(this.itemWidth/2),t=Math.floor(this.itemWidth/20),n=(this.weaponPrizeId-4)*this.itemWidth+s+this.randomRange(t,18*t);return this.weaponWrapper.current.style.transition=`left ${this.transitionDuration}s ease-out`,setTimeout(()=>{this.weaponWrapper.current.style.left=`-${n}px`},100),this.weaponPrizeId};setWinner=s=>{this.weapons[this.weaponPrizeId]=s}}const Fe=()=>{const r=W();return O(b.item.useDailyChest.mutationOptions({onSuccess:()=>{setTimeout(()=>{r.invalidateQueries({queryKey:b.user.getInventory.key()})},6e3)},onError:s=>{const t=s.message||"Unknown error occurred";console.error(t),h.error(t)}}))},Ue=({items:r,itemsCount:s,transitionDuration:t,isSpin:n,setIsSpin:i,handleClose:o})=>{const[m,p]=c.useState(r),[a,x]=c.useState(-1),[g,y]=c.useState(!1),[E,C]=c.useState([]),[k,S]=c.useState(!1),{mutate:j,data:f,isSuccess:u}=Fe(),N=K(),M=c.useRef(null),_=c.useRef(null),P=c.useRef(null),Q=()=>{C(E.concat(m[a])),i(!1),y(!0)};c.useLayoutEffect(()=>{const I=new Me({winner:null,weapons:r,rouletteContainerRef:M.current,weaponsRef:_,weaponsCount:s,transitionDuration:t,itemWidth:N?100:125});I.set_weapons(),p(I.weapons),P.current=I},[r,s,t]);const L=async()=>{j()};return c.useEffect(()=>{if(u&&f){const I=r.find(T=>T.itemId===f.itemId);I&&(P.current.setWinner(I),S(!0),i(!0),setTimeout(()=>{const T=P.current.spin();x(T)},1e3))}},[u,f,r]),e.jsx("div",{children:e.jsxs("div",{className:"rouletteWrapper",children:[e.jsx("div",{ref:M,children:e.jsxs("div",{className:"relative h-[160px] w-[700px] overflow-hidden rounded-[5px] border border-[#232730] md:w-[875px]",children:[e.jsx("div",{className:"evTarget"}),e.jsx("div",{ref:_,className:"evWeapons",onTransitionEnd:Q,children:m?.map((I,T)=>e.jsx(Oe,{id:T,isLoser:T!==a&&!n&&g,itemName:I.itemName,rarity:I.rarity,itemImage:I.itemImage,itemQuantity:I.itemQuantity},T))})]})}),k?e.jsx("div",{className:"mt-5 flex h-16 flex-col gap-2",children:E?.length>0&&e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs("p",{className:"font-body font-semibold text-lg text-white md:text-2xl",children:["You received ",e.jsxs("span",{className:"text-custom-yellow",children:[E[0]?.itemName," "]})," ","x",E[0]?.itemQuantity,"!"]}),e.jsx("button",{className:"mx-auto w-1/5 cursor-pointer rounded-md border border-gray-700 bg-red-600 p-2 px-5 text-stroke-sm text-white hover:brightness-95",onClick:()=>o(),children:"Close"})]})}):e.jsxs("button",{type:"button",disabled:n||k,className:"weapons-center darkBlueButtonBGSVG mx-auto mt-5 flex h-16 w-1/2 justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs disabled:opacity-75 disabled:brightness-75 md:w-3/4 dark:text-slate-200",onClick:L,children:[e.jsx("img",{className:"-ml-4 my-auto mr-2.5 inline-block size-8",src:"https://cloudflare-image.jamessut.workers.dev/ui-images/KWEkQyF.png",alt:""}),e.jsx("span",{className:"my-auto text-2xl",children:"Open"})]})]})})},We=({potentialChestItems:r,isLoading:s,isSpin:t,setIsSpin:n,handleClose:i})=>e.jsx("div",{className:"LootboxWrapper",children:s?e.jsx(ye,{center:!0}):e.jsx(Ue,{items:r,itemsCount:150,transitionDuration:5,isSpin:t,setIsSpin:n,handleClose:i})}),Oe=({id:r,itemName:s,rarity:t,itemImage:n,isLoser:i,itemQuantity:o})=>e.jsx("div",{className:"evWeapon",style:i?{opacity:"0.5"}:{opacity:"1"},children:e.jsxs("div",{className:"evWeaponInner",id:String(r),children:[e.jsx("div",{className:"evWeaponBorder"}),e.jsx("img",{className:"w-[70px]! h-[70px]! absolute! top-[25px]! left-[50%]! -translate-x-1/2",src:n,alt:s}),e.jsxs("span",{className:"-translate-x-1/2 absolute top-0 left-[25px] font-body font-semibold text-base text-blue-500 text-stroke-s-sm",children:[o,"x"]}),e.jsx("div",{className:"evWeaponRarity "+t}),e.jsx("div",{className:"evWeaponText mt-auto font-body font-semibold text-stroke-sm",children:e.jsx("p",{className:"text-sm! "+t,children:s})})]})});function Pe({openModal:r,setOpenModal:s}){const[t,n]=c.useState(!1),{data:i,isLoading:o}=qe(),m=()=>{s(!1)};return e.jsx(J,{open:r,showClose:!t,modalMaxWidth:"md:max-w-fit",contentPadding:"px-1 md:px-6 py-10",iconBackground:"shadow-lg",contentHeight:"overflow-x-hidden!",title:"Daily Chest",Icon:()=>e.jsx("img",{src:"https://d13cmcqz8qkryo.cloudfront.net/static/items/special/dailychest.png",alt:"",className:"mt-0.5 size-11"}),onOpenChange:t?null:m,children:e.jsx(We,{potentialChestItems:i,isLoading:o,isSpin:t,setIsSpin:n,handleClose:m})})}const Ae=()=>{const r=W();return{activateItem:O({mutationFn:async({currentUser:t,item:n})=>{if(n.health>0&&t.currentHealth===t.health){F.error("You're already full hp!");return}if(t.jailedUntil>0||t.hospitalisedUntil>0){F.error("You can't use this in your current state!");return}return await je.post(`${w.USER.USEITEM}`,{itemId:n.id})},onSuccess:async t=>{await r.invalidateQueries({queryKey:w.USER.INVENTORY}),await r.invalidateQueries({queryKey:[w.USER.CURRENTUSERINFO]}),console.log(t)},onError:t=>{F.error(t?.response?.data||"An error occurred")}})}},De=()=>{const r=W();return O(b.item.useMaterialsCrate.mutationOptions({onSuccess:()=>{setTimeout(()=>{r.invalidateQueries({queryKey:b.user.getInventory.key()}),r.invalidateQueries({queryKey:[w.USER.CURRENTUSERINFO]})},30),h.success("You redeemed 10 Raw Materials for your gang!")},onError:s=>{const t=s.message||"Unknown error occurred";console.error(t),h.error(t)}}))},_e=()=>{const r=W();return O(b.item.useToolsCrate.mutationOptions({onSuccess:()=>{setTimeout(()=>{r.invalidateQueries({queryKey:b.user.getInventory.key()}),r.invalidateQueries({queryKey:[w.USER.CURRENTUSERINFO]})},30),h.success("You redeemed 10 Tools for your gang!")},onError:s=>{const t=s.message||"Unknown error occurred";console.error(t),h.error(t)}}))};function Qe({open:r,setOpen:s,title:t,item:n,currentUser:i}){const[o,m]=c.useState(null),[p,a]=c.useState(""),[x,g]=c.useState("accuracy"),y=be(),{HOSPITALISE_ITEM_NAME:E,REVIVE_ITEM_NAME:C,JAIL_ITEM_NAME:k,MEGAPHONE_ITEM_NAME:S}=y,j=async()=>{let u,N;if(n.name===E){if(p===""){h.error("You must enter aN injury name!");return}u=w.SPECIALITEMS.DEATHNOTE,N={userId:o,injuryName:p,injuryType:x}}if(n.name===C&&(u=w.SPECIALITEMS.LIFENOTE,N={userId:o}),n.name===k){if(p===""){h.error("You must enter a reason!");return}u=w.SPECIALITEMS.KOMPROMAT,N={userId:o,reason:p}}if(n.name===S){if(!p||p.length<5){h.error("Enter a message with at least 5 characters!");return}u=w.SPECIALITEMS.MEGAPHONE,N={message:p}}else if(!o){h.error("You must enter a target student ID!");return}try{await X(u,N),h.success("Item used successfully"),s(!1)}catch(M){console.error("Failed to use item:",M),h.error(M.message||"Failed to use item")}},f=u=>{u.preventDefault(),j()};return e.jsx(J,{showClose:!0,open:r,title:t,iconBackground:"shadow-lg",Icon:()=>e.jsx("img",{src:"https://cloudflare-image.jamessut.workers.dev/ui-images/JXozQjh.png",alt:"",className:"mt-0.5 h-11 w-auto"}),onOpenChange:s,children:e.jsxs("form",{className:"mt-4 w-full max-w-lg",onSubmit:f,children:[n.name!==S&&e.jsx("div",{className:"-mx-3 flex flex-wrap md:mb-2",children:e.jsxs("div",{className:"mb-6 w-full px-3 md:mb-0",children:[e.jsxs("label",{className:"mb-2 block text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200",htmlFor:"studentid",children:["Target Student ID",e.jsx("span",{className:"text-red-500",children:" *"})]}),e.jsxs("div",{className:"mt-1 flex rounded-md shadow-xs",children:[e.jsx("span",{className:"inline-flex items-center rounded-l-md border border-gray-300 border-r-0 bg-gray-50 px-3 text-gray-500 sm:text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200",children:"#"}),e.jsx("input",{type:"number",name:"studentid",min:1,id:"studentid",className:"block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",placeholder:"0",onChange:u=>m(Number.parseInt(u.target.value))})]})]})}),n.name!==C&&n.name!==S&&e.jsx("div",{className:"-mx-3 flex flex-wrap md:mt-5",children:e.jsxs("div",{className:"mb-2 w-full px-3 md:mb-0",children:[e.jsxs("label",{className:"mb-2 block text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200",htmlFor:"grid-first-name",children:[n.name===E?"Injury Name":"Reason",e.jsx("span",{className:"text-red-500",children:" *"})]}),e.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:e.jsx("input",{type:"text",name:"text",id:"text",maxLength:100,className:"block w-full min-w-0 flex-1 rounded-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",placeholder:"",onChange:u=>a(u.target.value)})})]})}),n.name===S&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"-mt-2 text-center text-gray-200 text-lg",children:[e.jsx("p",{className:"text-custom-yellow",children:"Broadcast a global message to all students"}),e.jsx("p",{className:"text-red-400 text-xs",children:"Messages are monitored by Staff, inappropriate messages may result in punishment"})]}),e.jsx("div",{className:"-mx-3 flex flex-wrap md:mt-5",children:e.jsxs("div",{className:"mb-2 w-full px-3 md:mb-0",children:[e.jsxs("label",{className:"mb-2 flex flex-row items-center text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200",htmlFor:"grid-first-name",children:["Global Message",e.jsx("span",{className:"text-red-500",children:" *"}),e.jsx("small",{className:"my-auto ml-4 text-gray-400",children:"(Max length: 400 characters)"})]}),e.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:e.jsx("textarea",{id:"globalMessage",name:"globalMessage",rows:10,maxLength:i?.userType==="admin"?4e3:400,className:"block w-full min-w-0 flex-1 rounded-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",placeholder:"Add a message..",value:p,onChange:u=>a(u.target.value)})})]})})]}),n.name===E&&e.jsx("div",{className:"-mx-3 flex flex-wrap md:mt-5",children:e.jsx("div",{className:"mb-4 flex w-full px-3 md:mb-0",children:e.jsxs("fieldset",{className:"mx-auto mt-1 flex w-3/4 flex-col gap-3 rounded-md border-2 border-gray-600 bg-slate-800 px-4 py-1 text-gray-200 text-stroke-sm md:p-4",children:[e.jsxs("legend",{className:"text-stroke-sm!",children:["Select an Injury Debuff ",e.jsx("span",{className:"text-red-500",children:" *"})]}),e.jsxs("div",{children:[e.jsx("input",{type:"radio",id:"concussion",name:"injury",value:"concussion",checked:x==="accuracy",onChange:()=>g("accuracy")}),e.jsxs("label",{className:"ml-2 inline-flex font-medium",htmlFor:"concussion",children:["Concussion"," ",e.jsx("img",{src:D.concussion_injury?.icon,alt:"",className:"my-auto ml-2 h-5 w-auto"})]}),e.jsx("p",{className:"font-body font-semibold text-indigo-500 text-sm",children:"Increased chance to miss attacks"})]}),e.jsxs("div",{children:[e.jsx("input",{type:"radio",id:"bleed",name:"injury",value:"bleed",checked:x==="bleed",onChange:()=>g("bleed")}),e.jsxs("label",{className:"ml-2 inline-flex",htmlFor:"bleed",children:["Bleed",e.jsx("img",{src:D.bleeding_injury?.icon,alt:"",className:"my-auto ml-2 h-5 w-auto"})]}),e.jsx("p",{className:"font-body font-semibold text-indigo-500 text-sm",children:"HP loss each combat turn"})]}),e.jsxs("div",{children:[e.jsx("input",{type:"radio",id:"fracture",name:"injury",value:"fracture",checked:x==="damage",onChange:()=>g("damage")}),e.jsxs("label",{className:"ml-2 inline-flex font-medium",htmlFor:"fracture",children:["Fracture",e.jsx("img",{src:D.fracture_injury?.icon,alt:"",className:"my-auto ml-2 h-5 w-auto"})]}),e.jsx("p",{className:"font-body font-semibold text-indigo-500 text-sm",children:"Reduced damage output"})]}),e.jsxs("div",{children:[e.jsx("input",{className:"",type:"radio",id:"fatigue",name:"injury",value:"fatigue",checked:x==="ability_lock",onChange:()=>g("ability_lock")}),e.jsxs("label",{className:"ml-2 inline-flex font-medium",htmlFor:"fatigue",children:["Fatigue",e.jsx("img",{src:D.fatigue_injury?.icon,alt:"",className:"my-auto ml-2 h-5 w-auto"})]}),e.jsx("p",{className:"font-body font-semibold text-indigo-500 text-sm",children:"Can't use abilities"})]}),e.jsxs("div",{children:[e.jsx("input",{type:"radio",id:"muscle",name:"injury",value:"muscle",checked:x==="defence",onChange:()=>g("defence")}),e.jsxs("label",{className:"ml-2 inline-flex font-medium",htmlFor:"muscle",children:["Muscle Injury",e.jsx("img",{src:D.muscle_injury?.icon,alt:"",className:"my-auto ml-2 h-5 w-auto"})]}),e.jsx("p",{className:"font-body font-semibold text-indigo-500 text-sm",children:"Reduced defence"})]})]})})}),e.jsx("div",{className:"sm:mt-6",children:e.jsx("button",{type:"submit",className:"inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-base text-stroke-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:text-sm",children:"Use Item"})})]})})}function $({equippedItems:r}){const s=W(),t=K(),{isLoading:n,error:i,data:o}=Te(),[m,p]=c.useState("All"),[a,x]=c.useState(!1),[g,y]=c.useState({}),E=we("good_stomach"),[C,k]=c.useState(!1),{equipItem:S}=te();Ae();const{data:j}=Z(),[f,u]=c.useState(null),N=c.useRef(null),M=["Death Book","Megaphone","Life Book","Kompromat","Daily Chest","Small Raw Materials Crate","Small Tools Crate"],P=O({mutationFn:async l=>{const{item:d}=l;if(d.health>0&&j.currentHealth===j.health){h.error("You're already full hp!");return}if(j.jailedUntil>0||j.hospitalisedUntil>0){h.error("You can't use this in your current state!");return}const v=await X(w.USER.USEITEM,{userItemId:l.id});return d.health>0&&(s.invalidateQueries({queryKey:[w.USER.CURRENTUSERINFO]}),h.success(`You recovered ${d.health} HP!`)),d.energy>0&&(s.invalidateQueries({queryKey:[w.USER.CURRENTUSERINFO]}),h.success(`You recovered ${d.energy} Energy!`)),d.actionPoints>0&&(s.invalidateQueries({queryKey:[w.USER.CURRENTUSERINFO]}),h.success(`You recovered ${d.actionPoints} AP!`)),v},onError:l=>{console.log(l)},onSuccess:l=>{if(s.invalidateQueries({queryKey:w.USER.INVENTORY}),l.info.injuryRemoved){s.invalidateQueries({queryKey:w.USER.STATUSEFFECTS}),h.success("Injury treated successfully!");return}if(l.info.recipeUnlocked){h.success("Recipe unlocked successfully!");return}h.success("Item used successfully!"),s.invalidateQueries({queryKey:[w.USER.CURRENTUSERINFO]})}}),{mutate:Q}=De(),{mutate:L}=_e(),I=l=>{const{item:d}=l;if(j?.hospitalisedUntil>0){h.error("Cant use items while hospitalised!");return}if(j?.jailedUntil>0){h.error("Cant use items while jailed!");return}if(j?.missionEnds>0){h.error("Cant use items while on a mission!");return}if(d?.itemType==="special"){if(d?.name==="Daily Chest"){k(!0);return}if(d?.name==="Small Raw Materials Crate"){Q();return}if(d?.name==="Small Tools Crate"){L();return}x(!0),y(d)}else P.mutate(l)},T=l=>{S.mutate({currentUser:j,userItem:l})},se=l=>{const{equippedItems:d}=l,v=l.data;if(!v)return null;const{item:q}=v,oe=A=>["consumable","crafting","upgrade","quest","junk","recipe","pet"].includes(A),ce=A=>M.includes(A),de=A=>["consumable","special","recipe","pet"].includes(A),me=d?.[q.itemType]?.userItemId===v.id,ue=j?.level>=q.level,pe=()=>e.jsx(z,{className:"text-base",onClick:()=>I(v),children:"Use"}),xe=()=>e.jsx(z,{disabled:!ue,className:"text-base",onClick:()=>T(v),children:"Equip"});return e.jsx("div",{className:"flex size-full items-center justify-center",children:oe(q.itemType)||ce(q.name)?e.jsx(e.Fragment,{children:de(q.itemType)?pe():null}):e.jsx(e.Fragment,{children:me?e.jsx("p",{className:"m-auto inline font-semibold text-base text-custom-yellow md:text-sm",children:"Equipped"}):xe()})})};c.useEffect(()=>{N.current&&N.current.api&&(m==="All"?N.current.api.setFilterModel({itemTypeFilter:{type:"notEqual",filter:null}}):N.current.api.setFilterModel({itemTypeFilter:{type:"includes",filter:re[m]}}))},[m]);const ae=Ne.memo(l=>{const{value:d}=l,v=l.data.upgradeLevel||0;return e.jsxs("div",{className:"relative flex h-full items-center gap-3 px-1 py-2 md:w-full md:flex-row md:items-start md:gap-4 md:p-1",children:[e.jsx(ve,{itemTypeFrame:!0,item:l.data,className:"my-auto size-14"}),e.jsxs("div",{className:"flex flex-1 flex-col gap-1.5 py-1.5 md:my-auto md:gap-0",children:[e.jsxs("p",{className:R(d.name.length>15?"md:text-sm! text-[0.65rem]":"text-sm! md:text-base!","text-wrap! leading-none! truncate text-left font-semibold text-custom-yellow md:text-base"),children:[d.name,v>0&&e.jsxs("span",{children:[" +",v]})]}),e.jsx("p",{className:R(ee(d.rarity),"text-xs! leading-none! md:text-sm! text-left font-semibold"),children:B(d.itemType)}),e.jsx("div",{className:"mt-1 flex flex-col font-bold text-slate-700 text-xs dark:text-indigo-400",children:Ie(d,E).map(q=>e.jsx("p",{children:q},q))})]})]})}),re={All:[],Weapons:["weapon","ranged","offhand"],Armor:["head","chest","hands","legs","feet","finger","shield"],Consumables:["consumable","recipe","pet"],Material:["crafting","upgrade"],Misc:["special","quest","junk"]},B=l=>l==="weapon"?"Melee Weapon":l==="ranged"?"Ranged Weapon":l==="quest"?"Task Item":l==="crafting"?"Material":l==="special"?"Special Item":Ee(l);c.useEffect(()=>{o&&u(o)},[o]);const ne=[{headerName:"Item",field:"item",cellRenderer:ae,cellClass:"items-center! flex!",minWidth:t?183:250,sortable:!1,autoHeight:!0,wrapText:!0,getQuickFilterText:l=>l.data.item.itemType,filter:"agTextColumnFilter",valueFormatter:l=>l.data.item,filterValueGetter:l=>l.data.item.name,filterParams:{filterOptions:["contains"],defaultOption:"contains"}},{headerName:t?"Qty":"Quantity",field:"count",headerClass:"centerGridHeader",maxWidth:120,cellClass:"items-center! justify-center! flex! font-semibold font-body text-base",filter:"agNumberColumnFilter",filterParams:{filterOptions:["equals","greaterThan","lessThan","inRange"],defaultOption:"equals"}},{headerName:"Category",field:"item.itemType",headerClass:"centerGridHeader",hide:t,cellClass:"md:text-base text-sm font-semibold text-center flex! items-center! justify-center! truncate",valueFormatter:l=>B(l.data.item.itemType)},{headerName:"Level",field:"item.level",hide:t,initialSortIndex:1,maxWidth:120,sort:"desc",cellClass:"items-center! justify-center! flex! font-semibold font-body text-base",headerClass:"centerGridHeader",filterParams:{filterOptions:["equals","greaterThan","lessThan","inRange"],defaultOption:"equals"}},{headerName:"Actions",field:"actions",sortable:!1,headerClass:"centerGridHeader",cellClass:"flex! items-center! justify-center!",cellRenderer:se,cellRendererParams:{equippedItems:r},valueFormatter:l=>l.data,filter:!1,floatingFilter:!1},{headerName:"CategoryHidden",field:"itemTypeFilter",hide:!0,filterParams:{filterOptions:[{displayKey:"includes",displayName:"Includes",predicate:([l],d)=>l.includes(d)}]},filterValueGetter:l=>l.data.item?.itemType}],[ie,le]=c.useState(ne);return c.useEffect(()=>{const l={force:!0,suppressFlash:!0,columns:["actions"]};N?.current?.api?.refreshCells(l),le(d=>d.map(v=>v.field==="actions"?{...v,cellRendererParams:{equippedItems:r}}:v))},[r]),i?"An error has occurred: "+i.message:e.jsxs(e.Fragment,{children:[e.jsx(Le,{selectedTab:m,setSelectedTab:p}),e.jsx(Pe,{openModal:C,setOpenModal:k}),e.jsx(Qe,{open:a,setOpen:x,title:g.name,item:g,currentUser:j}),e.jsx("div",{className:"mb-8 md:mb-0 md:max-w-6xl 2xl:mx-auto",children:e.jsx(ke,{keyProp:JSON.stringify(r),dataList:f,colDefs:ie,isLoading:n,customGridRef:N})})]})}const Le=({selectedTab:r,setSelectedTab:s})=>{const t=i=>r===i,n=[{name:"All",current:t("All")},{name:"Weapons",current:t("Weapons")},{name:"Armor",current:t("Armor")},{name:"Consumables",current:t("Consumables")},{name:"Material",current:t("Material")},{name:"Misc",current:t("Misc")}];return e.jsxs("div",{children:[e.jsxs("div",{className:"mx-2 my-1 2xl:hidden",children:[e.jsx("label",{htmlFor:"tabs",className:"sr-only",children:"Select a tab"}),e.jsx("select",{id:"tabs",name:"tabs",className:"mb-3 block w-full rounded-md border-gray-300 px-2 text-stroke-md focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white",defaultValue:n.find(i=>i.current).name,onChange:i=>s(i.target.value),children:n.map(i=>e.jsx("option",{children:i.name},i.name))})]}),e.jsx("div",{className:"hidden 2xl:block",children:e.jsx("nav",{className:"relative z-0 flex divide-x divide-gray-200 border-gray-600 border-b shadow-sm dark:divide-gray-600","aria-label":"Tabs",children:n.map((i,o)=>e.jsxs("button",{"aria-current":i.current?"page":void 0,className:R(i.current?"text-gray-900":"text-gray-500 hover:text-gray-700",o===0?"rounded-tl-lg":"",o===n.length-1?"rounded-tr-lg":"","group relative min-w-0 flex-1 overflow-hidden bg-white p-4 text-center font-medium text-sm hover:bg-gray-50 focus:z-10 dark:bg-gray-800 dark:text-white"),onClick:()=>s(i.name),children:[e.jsx("span",{children:i.name}),e.jsx("span",{"aria-hidden":"true",className:R(i.current?"bg-indigo-500":"bg-transparent","absolute inset-x-0 bottom-0 h-0.5")})]},i.name))})})]})};function Ye(){const[r,s]=c.useState("/inventory"),[t,n]=c.useState("head"),i=K(!0),o=Ce(),m=Se();c.useLayoutEffect(()=>{o.pathname&&o.pathname!==r&&s(o.pathname)},[o.pathname]);const{data:p,error:a,isLoading:x}=Z(),{data:g}=H(b.user.getInventory.queryOptions()),{data:y}=H(b.user.getEquippedItems.queryOptions());if(x)return null;if(a)return"An error has occurred: "+a.message;const C=["weapon","offhand","ranged"].includes(t)?"damage":"armour",k=t==="shield"?"offhand":null,S=g?.filter(f=>f.item?.itemType===t||f.item?.itemType===k)?.sort((f,u)=>f.item[C]>u.item[C]?-1:f.item[C]<u.item[C]?1:0)||[];y&&y.shield&&(y.offhand=y.shield);const j=[{name:"/inventory",text:"Items",current:o.pathname==="/inventory"},{name:"/equipment",text:"Equipment",current:o.pathname==="/equipment"}];return e.jsxs("div",{className:"flex h-full flex-col gap-2 md:mx-auto md:max-w-208 2xl:max-w-(--breakpoint-xl) 2xl:flex-row",children:[e.jsxs("div",{children:[e.jsx(Re,{inventory:S,currentUser:p,equippedItems:y||[]}),e.jsx("div",{className:"block 2xl:hidden",children:e.jsx("nav",{className:"relative z-0 flex divide-x divide-gray-700 shadow-sm","aria-label":"Tabs",children:j.map((f,u)=>e.jsxs("a",{"aria-current":f.current?"page":void 0,className:R(f.current?"text-gray-900":"text-gray-500 hover:text-gray-700",u===j.length-1?"md:rounded-r-lg":"",u===0?"md:rounded-l-lg":"","group relative min-w-0 flex-1 cursor-pointer overflow-hidden bg-white p-4 text-center font-medium text-sm hover:bg-gray-50 focus:z-10 dark:bg-gray-800 dark:text-slate-200"),onClick:()=>m(f.name),children:[e.jsx("span",{children:f.text}),e.jsx("span",{"aria-hidden":"true",className:R(f.current?"bg-indigo-500":"bg-transparent","absolute inset-x-0 bottom-0 h-0.5")})]},f.name))})})]}),typeof i=="number"&&i<=1536?r==="/inventory"?e.jsx($,{currentUser:p,equippedItems:y}):e.jsx("div",{className:"flex flex-col gap-4",children:e.jsx(Y,{mobile:!0,equippedItems:y,setEquipTooltipFilter:n})}):e.jsxs("div",{className:"flex w-full gap-6",children:[e.jsx("div",{className:"2xl:mt-0 2xl:w-[75%]",children:e.jsx($,{currentUser:p,equippedItems:y})}),e.jsx("div",{className:"2xl:w-[30%]",children:e.jsx(Y,{equippedItems:y,setEquipTooltipFilter:n})})]})]})}export{Ye as default};
