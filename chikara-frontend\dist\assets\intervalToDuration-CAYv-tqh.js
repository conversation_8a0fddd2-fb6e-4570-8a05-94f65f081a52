import{cC as Y,cD as y,cE as _,cF as M,cG as p,cH as A,cI as N,cJ as T,cK as D,cL as H,cM as S}from"./index-DC0BXKDb.js";import{d as b}from"./differenceInHours-CqXSCWk0.js";function C(t,e,o){const n=Y(t,o?.in);if(isNaN(e))return y(t,NaN);if(!e)return n;const s=n.getDate(),c=y(t,n.getTime());c.setMonth(n.getMonth()+e+1,0);const i=c.getDate();return s>=i?c:(n.setFullYear(c.getFullYear(),c.getMonth(),s),n)}function f(t,e,o){const{years:n=0,months:s=0,weeks:c=0,days:i=0,hours:a=0,minutes:r=0,seconds:g=0}=e,d=Y(t,o?.in),u=s||n?C(d,s+n*12):d,l=i||c?_(u,i+c*7):u,h=r+a*60,m=(g+h*60)*1e3;return y(t,+l+m)}function L(t,e,o){const[n,s]=M(o?.in,t,e);return n.getFullYear()-s.getFullYear()}function z(t,e,o){const[n,s]=M(o?.in,t,e),c=F(n,s),i=Math.abs(p(n,s));n.setDate(n.getDate()-c*i);const a=+(F(n,s)===-c),r=c*(i-a);return r===0?0:r}function F(t,e){const o=t.getFullYear()-e.getFullYear()||t.getMonth()-e.getMonth()||t.getDate()-e.getDate()||t.getHours()-e.getHours()||t.getMinutes()-e.getMinutes()||t.getSeconds()-e.getSeconds()||t.getMilliseconds()-e.getMilliseconds();return o<0?-1:o>0?1:o}function O(t,e,o){const n=A(t,e)/N;return T(o?.roundingMethod)(n)}function W(t,e,o){const[n,s]=M(o?.in,t,e),c=D(n,s),i=Math.abs(L(n,s));n.setFullYear(1584),s.setFullYear(1584);const a=D(n,s)===-c,r=c*(i-+a);return r===0?0:r}function k(t,e){const[o,n]=M(t,e.start,e.end);return{start:o,end:n}}function x(t,e){const{start:o,end:n}=k(e?.in,t),s={},c=W(n,o);c&&(s.years=c);const i=f(o,{years:s.years}),a=H(n,i);a&&(s.months=a);const r=f(i,{months:s.months}),g=z(n,r);g&&(s.days=g);const d=f(r,{days:s.days}),u=b(n,d);u&&(s.hours=u);const l=f(d,{hours:s.hours}),h=O(n,l);h&&(s.minutes=h);const I=f(l,{minutes:s.minutes}),m=S(n,I);return m&&(s.seconds=m),s}export{x as i};
