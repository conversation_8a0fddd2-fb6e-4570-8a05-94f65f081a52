import{F as w,d as j,e as p,o as r,k as c,r as h,j as e,ad as G,cY as C,g as b,a3 as f,X as N,t as I,b as v,cZ as S,n as q,C as o,L as F,c_ as L,I as D}from"./index-DC0BXKDb.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=[["circle",{cx:"11",cy:"13",r:"9",key:"hd149"}],["path",{d:"M14.35 4.65 16.3 2.7a2.41 2.41 0 0 1 3.4 0l1.6 1.6a2.4 2.4 0 0 1 0 3.4l-1.95 1.95",key:"jp4j1b"}],["path",{d:"m22 2-1.5 1.5",key:"ay92ug"}]],B=w("bomb",M),Q=()=>{const s=j();return{acceptInvite:p(r.gang.acceptInvite.mutationOptions({onSuccess:()=>{c.success("Gang joined!"),s.invalidateQueries({queryKey:r.user.getCurrentUser.key()}),s.invalidateQueries({queryKey:r.gang.getCurrentInvites.key()})},onError:a=>{c.error(a?.message||"An error occurred")}})).mutate}},z=()=>{const s=j();return{declineInvite:p(r.gang.declineInvite.mutationOptions({onSuccess:()=>{s.invalidateQueries({queryKey:r.gang.getCurrentInvites.key()})},onError:a=>{c.error(a?.message||"An error occurred")}})).mutate}},A=s=>{const[t,a]=h.useState(""),[l,i]=h.useState(""),n=j(),d=p(r.gang.createGang.mutationOptions({onSuccess:()=>{c.success("Gang created!"),n.invalidateQueries({queryKey:r.user.getCurrentUser.key()}),s(!1)},onError:m=>{c.error(m?.message||"An error occurred")}}));return{gangName:t,setGangName:a,gangDescription:l,setGangDescription:i,createGang:()=>{if(t.length<4){c.error("Gang name must be at least 4 characters");return}d.mutate({name:t,description:l})}}};function O({open:s,setOpen:t,level:a,hasGangItem:l=!1}){const{gangName:i,setGangName:n,gangDescription:d,setGangDescription:y,createGang:m}=A(t),k=()=>{m()},x=l,g=a>=20;return e.jsx(G,{showClose:!0,open:s,title:"Create a Gang",iconBackground:"shadow-lg",modalMaxWidth:"max-w-3xl!",Icon:()=>e.jsx("img",{src:"https://cloudflare-image.jamessut.workers.dev/ui-images/Su197a0.png",alt:"",className:"mt-0.5 h-10 w-auto"}),onOpenChange:t,children:e.jsxs("div",{className:"flex flex-col md:mx-12",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"mb-2 block text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200",htmlFor:"inputtitle",children:["Gang Name",e.jsx("span",{className:"text-red-500",children:" *"})]}),e.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:e.jsx("input",{type:"text",name:"inputtitle",id:"inputtitle",className:"block w-full min-w-0 flex-1 rounded-md border-gray-300 px-3 py-2 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",maxLength:"40",value:i,onChange:u=>{n(u.target.value)}})})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"about",className:"mt-4 mb-2 block font-bold text-gray-700 text-sm uppercase tracking-wide dark:font-normal dark:text-gray-300",children:"Gang Description"}),e.jsx("div",{className:"mt-1 mb-4",children:e.jsx("textarea",{id:"about",name:"about",rows:2,maxLength:"250",className:"mt-1 block h-20 w-full rounded-md border-gray-300 shadow-xs focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",value:d,onChange:u=>{y(u.target.value)}})})]}),e.jsxs("div",{className:"mx-4 my-2 flex flex-col rounded-lg border border-gray-600 bg-gray-900 p-2 text-center text-stroke-s-sm text-white",children:[e.jsx("p",{className:"font-body font-semibold text-white underline underline-offset-2",children:"Requirements:"}),e.jsxs("div",{className:"-mt-1 mx-auto flex gap-2 pr-8 text-base",children:[e.jsx("img",{src:C,alt:"",className:"mt-0.5 h-10 w-auto"}),e.jsxs("div",{className:b(x?"text-green-500":"text-red-500","my-auto"),children:["1x Gang Sigil"," ",x?e.jsx(f,{className:"mb-0.5 ml-1 inline size-4"}):e.jsx(N,{className:"mb-0.5 ml-1 inline size-4"})]})]}),e.jsx("div",{className:"mx-auto flex gap-2 text-base",children:e.jsxs("div",{className:b(g?"text-green-500":"text-red-500","my-auto"),children:[e.jsx(B,{className:"mr-1.5 mb-0.5 inline size-4 text-custom-yellow"}),"Level 20"," ",g?e.jsx(f,{className:"mb-0.5 ml-1 inline size-4"}):e.jsx(N,{className:"mb-0.5 ml-1 inline size-4"})]})})]}),e.jsx("button",{type:"button",disabled:!x||!g,className:"darkBlueButtonBGSVG mx-auto mt-2 flex h-18 w-3/4 items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs disabled:opacity-75 disabled:grayscale md:mt-3 dark:text-slate-200",onClick:()=>k(),children:"Create Gang"})]})})}function K(){const[s,t]=h.useState(!1),{data:a}=I(),{data:l}=v(r.gang.hasGangSigil.queryOptions({enabled:!a?.gangId}));return e.jsx(e.Fragment,{children:a?.gangId?e.jsx(S,{currentUser:a}):e.jsxs(e.Fragment,{children:[" ",e.jsx(O,{open:s,setOpen:t,text:"Create a Gang",subtitle:"Create a gang to join",level:a?.level,hasGangItem:l}),e.jsx("section",{className:"mx-auto rounded-lg bg-gray-100 py-6 md:max-w-3xl dark:bg-gray-800",children:e.jsx("div",{className:"container mx-auto px-0 md:px-6",children:e.jsx("div",{className:"mx-auto max-w-xl text-center",children:e.jsx("div",{className:"-mx-5 rounded-lg border border-gray-600 bg-white py-5 text-left shadow-xl md:mx-0 md:p-6 dark:bg-gray-900",children:e.jsx("div",{className:"flex flex-col items-center justify-center",children:e.jsxs("div",{className:"w-full px-4",children:[" ",e.jsx("p",{className:"text-center",children:"You are not in a gang!"}),e.jsxs("div",{className:"mt-4 flex items-center justify-center gap-4",children:[e.jsx(q,{to:"/ganglist",children:e.jsx(o,{children:"All Gangs"})}),e.jsx(o,{onClick:()=>t(!0),children:"Form a Gang"})]}),e.jsx(E,{})]})})})})})})]})})}const E=()=>{const{data:s,isLoading:t}=v(r.gang.getCurrentInvites.queryOptions()),{acceptInvite:a}=Q(),{declineInvite:l}=z(),i=n=>n===0?5:7;return t?null:e.jsx(e.Fragment,{children:e.jsxs("div",{className:"mt-4 flex w-full flex-col items-center",children:[e.jsx("hr",{className:"mb-2 w-full border border-gray-600/75"}),e.jsx("p",{className:"mb-2",children:"Your invites:"}),e.jsx(F,{isLoading:t,children:s?.length>0?e.jsx("div",{className:" flex w-full flex-col justify-center gap-2 text-center text-custom-yellow text-sm",children:s?.map(n=>e.jsxs("div",{className:"flex w-full gap-2",children:[e.jsxs("div",{className:"my-auto mb-1 flex h-14 w-3/4 items-center gap-2 rounded-lg bg-blue-800 p-2 md:py-2.5",children:[e.jsx(L,{src:n.gang,className:"rounded-md! my-auto h-6"}),e.jsxs("div",{className:"text-left",children:[e.jsx("p",{className:"truncate",children:n.gang.name}),e.jsxs("p",{className:"text-gray-200 text-xs",children:[n.gang.memberCount,"/",i(n?.gang?.hideout_level)," Members"]})]}),e.jsxs("div",{className:"ml-auto",children:[e.jsx("img",{src:"https://cloudflare-image.jamessut.workers.dev/ui-images/iQwXWBw.png",alt:"",className:"m-auto size-8 grayscale"}),e.jsx("p",{className:"text-center text-gray-200 text-xs leading-none",children:"Unranked"})]})]}),e.jsxs("div",{className:"mt-1.5 flex w-2/5 flex-1 gap-2",children:[e.jsx(o,{className:"text-sm!",onClick:()=>a(n.id),children:"Accept"}),e.jsx(o,{className:"text-sm!",type:"danger",onClick:()=>l(n.id),children:"Decline"})]})]},n.id))}):e.jsxs("p",{className:" flex w-full justify-center gap-2 text-center text-amber-500 text-sm",children:[e.jsxs("span",{className:"-ml-3 my-auto text-amber-400",children:[" ",e.jsx(D,{})]}),"No invites yet. Find a Gang in the Gangs list!"]})})]})})};export{K as default};
