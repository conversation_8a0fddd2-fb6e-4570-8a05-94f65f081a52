import{j as t,bN as M,bO as p,g as d,bR as T,r as g,d as E,e as h,o as j,ao as N,y as m}from"./index-DC0BXKDb.js";import{u as q}from"./useGetUnlockedTalents-CUtwY_Cf.js";import{t as f}from"./talentData-CZzZ7076.js";function C({abilityInfo:e,setAbilityInfo:i,setAbilitySelected:a,unequipAbility:n,abilitySelected:l}){return t.jsx(M,{children:t.jsxs(p.div,{initial:{y:"100%"},animate:{y:0},transition:{ease:"easeIn",duration:.01},className:d("dark talentInfoModalAnim fixed inset-x-0 bottom-0 z-300 min-h-[20%] w-full transform-none overflow-y-auto border-2 border-yellow-600 bg-gray-800 px-4 pt-1 pb-4 text-stroke-sm transition-transform md:bottom-5 md:left-[31.5%] md:w-1/3"),children:[t.jsxs("h5",{className:d("mt-0.5 mb-1 inline-flex items-center text-2xl uppercase",e?.talentType==="locked"?"text-gray-500":"text-indigo-600 dark:text-indigo-500"),children:[t.jsx(p.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:t.jsx("img",{alt:"",src:e?.image,className:d("mr-2.5 h-14 rounded-md text-gray-500 dark:text-gray-200",e?.talentType==="locked"?"w-10 brightness-90":"w-14")})},e?.image),t.jsxs("div",{children:[e?.talentType==="ability"&&t.jsx("p",{className:"text-orange-400 text-stroke-sm text-xs",children:"Ability"}),e?.talentType==="passive"&&t.jsx("p",{className:"text-sky-400 text-stroke-sm text-xs",children:"Passive"}),e?.displayName?e?.displayName:e?.name]})]}),t.jsxs("button",{type:"button",className:"absolute top-2.5 right-2.5 inline-flex size-8 items-center justify-center rounded-lg bg-gray-600 text-slate-200 text-sm hover:bg-gray-200 hover:text-gray-900 md:bg-transparent dark:hover:bg-gray-600 dark:hover:text-white",onClick:()=>{i(null),a(null)},children:[t.jsx("svg",{className:"size-3","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 14 14",children:t.jsx("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"})}),t.jsx("span",{className:"sr-only",children:"Close menu"})]}),t.jsx("p",{className:"max-w-lg text-gray-500 text-sm dark:text-gray-400",children:typeof e?.description=="function"?e.description({tier1Modifier:e.tier1Modifier||0,tier2Modifier:e.tier2Modifier||0,tier3Modifier:e.tier3Modifier||0,secondaryModifier:e.secondaryModifier||0}):e?.description}),e?.subDescription&&t.jsx("p",{className:"max-w-lg text-gray-500 text-sm dark:text-gray-400",children:typeof e?.subDescription=="function"?e.subDescription({tier1Modifier:e.tier1Modifier||0,tier2Modifier:e.tier2Modifier||0,tier3Modifier:e.tier3Modifier||0,secondaryModifier:e.secondaryModifier||0}):e?.subDescription}),e?.staminaCost&&e.staminaCost>0&&t.jsxs("p",{className:"max-w-lg text-orange-400 text-sm dark:text-orange-400",children:[e.staminaCost," STA"]}),e&&!l&&t.jsx("div",{className:"mt-6 flex w-full flex-col",children:t.jsx("button",{className:d("mx-auto rounded-lg border border-gray-900 px-4 py-2 text-center font-medium text-base text-stroke-sm focus:outline-hidden focus:ring-4","bg-red-700 text-white hover:bg-red-800 focus:ring-red-300 dark:bg-red-600 dark:focus:ring-red-800 dark:hover:bg-red-700"),onClick:()=>n?.mutate?.(e?.id),children:"Unequip"})})]})})}function U(){const{data:e}=q({queryKey:m.TALENTS.GETUNLOCKEDTALENTS,select:s=>({talentList:s?.talentList?.filter(r=>r.talentInfo.staminaCost>0),treePoints:s?.treePoints})}),{data:i}=T(),[a,n]=g.useState(null),[l,o]=g.useState(null),u=E(),x=i?[i[0]?.id,i[1]?.id,i[2]?.id,i[3]?.id]:[],k=i?[i[0],i[1],i[2],i[3]]:[],v=h({mutationFn:async s=>a?await N.post(m.TALENTS.EQUIPABILITY,{talentId:a,slot:s}):void 0,onSuccess:()=>{u.invalidateQueries(j.talents.getEquippedAbilities.queryOptions()),n(null),o(null)}}),y=h({mutationFn:async s=>{const r=x.findIndex(b=>b===s)+1,c=JSON.stringify({slot:r});return await N.post(`${m.TALENTS.UNEQUIPABILITY}`,c)},onSuccess:()=>{u.invalidateQueries(j.talents.getEquippedAbilities.queryOptions()),n(null),o(null)}}),A=s=>{const r={...s,image:s.image??void 0};a&&a===r.id?(n(null),o(null)):(n(r.id),o(r))},w=e?.talentList?.map(s=>s.talentInfo);return t.jsxs("div",{className:"p-2 md:mx-auto md:max-w-6xl",children:[t.jsx("p",{className:"text-slate-300",children:"Equipped Abilities"}),a&&t.jsx("div",{className:"overlayDim"}),t.jsx("div",{className:"mx-2 mt-2 grid grid-cols-2 grid-rows-2 gap-4 rounded-lg border-2 border-slate-500 bg-blue-900 p-2 shadow-xl",children:k.map((s,r)=>t.jsx("div",{className:d(a&&"cursor-pointer ring-2 ring-blue-300 hover:ring-blue-200","z-40 flex h-20 w-full rounded-md bg-slate-800"),onClick:()=>a&&v.mutate(r+1),children:s?t.jsx("img",{className:"m-auto w-20 cursor-pointer rounded-full p-2",src:f[s.name]?.image||s?.image||"",alt:"",onClick:c=>{c.stopPropagation(),o(s)}}):t.jsx("p",{className:"m-auto text-slate-300",children:"Empty"})},r))}),t.jsx("p",{className:"my-2 text-slate-300",children:"Available Abilities"}),t.jsx("div",{className:"grid grid-cols-5 gap-4",children:w?.map(s=>s&&!x?.includes(s?.id)&&t.jsx("div",{className:"z-40",children:t.jsx("img",{alt:"",src:f[s.name]?.image||(s.image??void 0)||"",className:d(a===s.id&&"z-40 ring-4 ring-blue-500","w-20 cursor-pointer rounded-full"),onClick:()=>A(s)})},s.id))}),l&&t.jsx(C,{abilityInfo:l,setAbilityInfo:o,setAbilitySelected:n,unequipAbility:y,abilitySelected:a})]})}export{U as default};
