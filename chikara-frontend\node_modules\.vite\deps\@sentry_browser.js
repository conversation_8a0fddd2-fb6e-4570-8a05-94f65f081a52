import {
  BrowserClient,
  OpenFeatureIntegrationHook,
  SDK_VERSION,
  SEMANTIC_ATTRIBUTE_SENTRY_OP,
  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,
  SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,
  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,
  Scope,
  WINDOW,
  addBreadcrumb,
  addEventProcessor,
  addIntegration,
  breadcrumbsIntegration,
  browserApiErrorsIntegration,
  browserProfilingIntegration,
  browserSessionIntegration,
  browserTracingIntegration,
  buildLaunchDarklyFlagUsedHandler,
  captureConsoleIntegration,
  captureEvent,
  captureException,
  captureFeedback,
  captureMessage,
  captureSession,
  chromeStackLineParser,
  close,
  consoleLoggingIntegration,
  contextLinesIntegration,
  continueTrace,
  createTransport,
  createUserFeedbackEnvelope,
  dedupeIntegration,
  defaultRequestInstrumentationOptions,
  defaultStackLineParsers,
  defaultStackParser,
  diagnoseSdkConnectivity,
  endSession,
  eventFiltersIntegration,
  eventFromException,
  eventFromMessage,
  exceptionFromError,
  extraErrorDataIntegration,
  featureFlagsIntegration,
  feedbackAsyncIntegration,
  feedbackSyncIntegration,
  flush,
  forceLoad,
  functionToStringIntegration,
  geckoStackLineParser,
  getActiveSpan,
  getClient,
  getCurrentScope,
  getDefaultIntegrations,
  getFeedback,
  getGlobalScope,
  getIsolationScope,
  getReplay,
  getRootSpan,
  getSpanDescendants,
  getSpanStatusFromHttpCode,
  getTraceData,
  globalHandlersIntegration,
  graphqlClientIntegration,
  httpClientIntegration,
  httpContextIntegration,
  inboundFiltersIntegration,
  init,
  instrumentOutgoingRequests,
  instrumentSupabaseClient,
  isEnabled,
  isInitialized,
  lastEventId,
  launchDarklyIntegration,
  lazyLoadIntegration,
  linkedErrorsIntegration,
  log_exports,
  makeBrowserOfflineTransport,
  makeFetchTransport,
  makeMultiplexedTransport,
  moduleMetadataIntegration,
  onLoad,
  openFeatureIntegration,
  opera10StackLineParser,
  opera11StackLineParser,
  parameterize,
  registerSpanErrorInstrumentation,
  replayCanvasIntegration,
  replayIntegration,
  reportingObserverIntegration,
  rewriteFramesIntegration,
  sendFeedback,
  setContext,
  setCurrentClient,
  setExtra,
  setExtras,
  setHttpStatus,
  setMeasurement,
  setTag,
  setTags,
  setUser,
  showReportDialog,
  spanToBaggageHeader,
  spanToJSON,
  spanToTraceHeader,
  spotlightBrowserIntegration,
  startBrowserTracingNavigationSpan,
  startBrowserTracingPageLoadSpan,
  startInactiveSpan,
  startNewTrace,
  startSession,
  startSpan,
  startSpanManual,
  statsigIntegration,
  supabaseIntegration,
  suppressTracing,
  thirdPartyErrorFilterIntegration,
  unleashIntegration,
  updateSpanName,
  winjsStackLineParser,
  withActiveSpan,
  withIsolationScope,
  withScope,
  zodErrorsIntegration
} from "./chunk-AAUQKRJB.js";
import "./chunk-G3PMV62Z.js";
export {
  BrowserClient,
  OpenFeatureIntegrationHook,
  SDK_VERSION,
  SEMANTIC_ATTRIBUTE_SENTRY_OP,
  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,
  SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,
  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,
  Scope,
  WINDOW,
  addBreadcrumb,
  addEventProcessor,
  addIntegration,
  breadcrumbsIntegration,
  browserApiErrorsIntegration,
  browserProfilingIntegration,
  browserSessionIntegration,
  browserTracingIntegration,
  buildLaunchDarklyFlagUsedHandler,
  captureConsoleIntegration,
  captureEvent,
  captureException,
  captureFeedback,
  captureMessage,
  captureSession,
  chromeStackLineParser,
  close,
  consoleLoggingIntegration,
  contextLinesIntegration,
  continueTrace,
  createTransport,
  createUserFeedbackEnvelope,
  dedupeIntegration,
  defaultRequestInstrumentationOptions,
  defaultStackLineParsers,
  defaultStackParser,
  diagnoseSdkConnectivity,
  endSession,
  eventFiltersIntegration,
  eventFromException,
  eventFromMessage,
  exceptionFromError,
  extraErrorDataIntegration,
  featureFlagsIntegration,
  feedbackAsyncIntegration,
  feedbackSyncIntegration as feedbackIntegration,
  feedbackSyncIntegration,
  flush,
  forceLoad,
  functionToStringIntegration,
  geckoStackLineParser,
  getActiveSpan,
  getClient,
  getCurrentScope,
  getDefaultIntegrations,
  getFeedback,
  getGlobalScope,
  getIsolationScope,
  getReplay,
  getRootSpan,
  getSpanDescendants,
  getSpanStatusFromHttpCode,
  getTraceData,
  globalHandlersIntegration,
  graphqlClientIntegration,
  httpClientIntegration,
  httpContextIntegration,
  inboundFiltersIntegration,
  init,
  instrumentOutgoingRequests,
  instrumentSupabaseClient,
  isEnabled,
  isInitialized,
  lastEventId,
  launchDarklyIntegration,
  lazyLoadIntegration,
  linkedErrorsIntegration,
  log_exports as logger,
  makeBrowserOfflineTransport,
  makeFetchTransport,
  makeMultiplexedTransport,
  moduleMetadataIntegration,
  onLoad,
  openFeatureIntegration,
  opera10StackLineParser,
  opera11StackLineParser,
  parameterize,
  registerSpanErrorInstrumentation,
  replayCanvasIntegration,
  replayIntegration,
  reportingObserverIntegration,
  rewriteFramesIntegration,
  sendFeedback,
  setContext,
  setCurrentClient,
  setExtra,
  setExtras,
  setHttpStatus,
  setMeasurement,
  setTag,
  setTags,
  setUser,
  showReportDialog,
  spanToBaggageHeader,
  spanToJSON,
  spanToTraceHeader,
  spotlightBrowserIntegration,
  startBrowserTracingNavigationSpan,
  startBrowserTracingPageLoadSpan,
  startInactiveSpan,
  startNewTrace,
  startSession,
  startSpan,
  startSpanManual,
  statsigIntegration,
  supabaseIntegration,
  suppressTracing,
  thirdPartyErrorFilterIntegration,
  unleashIntegration,
  updateSpanName,
  winjsStackLineParser,
  withActiveSpan,
  withIsolationScope,
  withScope,
  zodErrorsIntegration
};
