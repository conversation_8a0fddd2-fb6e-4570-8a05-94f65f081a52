import{W as je,r as y,$ as re,j as a,bS as we,_ as T,a1 as Ne,Y as jt,a0 as $e,bT as wt,l as P,bU as Nt,bV as $t,F as se,b as W,o as _,bW as fe,n as Pt,aH as Pe,bX as St,g as I,ax as Se,d as ae,e as ne,y as _e,G as _t,bY as It,aB as Rt,M as Ct,bZ as kt,ae as At,O as Ut,b_ as Et,L as Lt}from"./index-DC0BXKDb.js";import{u as Mt,Q as J,g as Ie}from"./useGetQuestObjectiveText-DejZoSM0.js";import{g as Re}from"./getUserItemCount-CIJt4adY.js";import{u as Ce}from"./useGetInventory-4cW0XzoG.js";import{B as oe}from"./book-open-CWI-FDJw.js";import{C as Tt}from"./chevron-up-CjBLp6J4.js";var F="Collapsible",[Qt,ke]=je(F),[zt,ie]=Qt(F),Ae=y.forwardRef((e,t)=>{const{__scopeCollapsible:r,open:s,defaultOpen:n,disabled:o,onOpenChange:i,...c}=e,[d,u]=re({prop:s,defaultProp:n??!1,onChange:i,caller:F});return a.jsx(zt,{scope:r,disabled:o,contentId:we(),open:d,onOpenToggle:y.useCallback(()=>u(p=>!p),[u]),children:a.jsx(T.div,{"data-state":le(d),"data-disabled":o?"":void 0,...c,ref:t})})});Ae.displayName=F;var Ue="CollapsibleTrigger",Ee=y.forwardRef((e,t)=>{const{__scopeCollapsible:r,...s}=e,n=ie(Ue,r);return a.jsx(T.button,{type:"button","aria-controls":n.contentId,"aria-expanded":n.open||!1,"data-state":le(n.open),"data-disabled":n.disabled?"":void 0,disabled:n.disabled,...s,ref:t,onClick:Ne(e.onClick,n.onOpenToggle)})});Ee.displayName=Ue;var ce="CollapsibleContent",Le=y.forwardRef((e,t)=>{const{forceMount:r,...s}=e,n=ie(ce,e.__scopeCollapsible);return a.jsx(jt,{present:r||n.open,children:({present:o})=>a.jsx(Gt,{...s,ref:t,present:o})})});Le.displayName=ce;var Gt=y.forwardRef((e,t)=>{const{__scopeCollapsible:r,present:s,children:n,...o}=e,i=ie(ce,r),[c,d]=y.useState(s),u=y.useRef(null),p=$e(t,u),l=y.useRef(0),f=l.current,m=y.useRef(0),v=m.current,j=i.open||c,N=y.useRef(j),$=y.useRef(void 0);return y.useEffect(()=>{const x=requestAnimationFrame(()=>N.current=!1);return()=>cancelAnimationFrame(x)},[]),wt(()=>{const x=u.current;if(x){$.current=$.current||{transitionDuration:x.style.transitionDuration,animationName:x.style.animationName},x.style.transitionDuration="0s",x.style.animationName="none";const S=x.getBoundingClientRect();l.current=S.height,m.current=S.width,N.current||(x.style.transitionDuration=$.current.transitionDuration,x.style.animationName=$.current.animationName),d(s)}},[i.open,s]),a.jsx(T.div,{"data-state":le(i.open),"data-disabled":i.disabled?"":void 0,id:i.contentId,hidden:!j,...o,ref:p,style:{"--radix-collapsible-content-height":f?`${f}px`:void 0,"--radix-collapsible-content-width":v?`${v}px`:void 0,...e.style},children:j&&n})});function le(e){return e?"open":"closed"}var qt=Ae,Ot=Ee,Dt=Le,R="Accordion",Ht=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[de,Wt,Ft]=Nt(R),[B,qn]=je(R,[Ft,ke]),ue=ke(),Me=P.forwardRef((e,t)=>{const{type:r,...s}=e,n=s,o=s;return a.jsx(de.Provider,{scope:e.__scopeAccordion,children:r==="multiple"?a.jsx(Xt,{...o,ref:t}):a.jsx(Kt,{...n,ref:t})})});Me.displayName=R;var[Te,Bt]=B(R),[Qe,Vt]=B(R,{collapsible:!1}),Kt=P.forwardRef((e,t)=>{const{value:r,defaultValue:s,onValueChange:n=()=>{},collapsible:o=!1,...i}=e,[c,d]=re({prop:r,defaultProp:s??"",onChange:n,caller:R});return a.jsx(Te,{scope:e.__scopeAccordion,value:P.useMemo(()=>c?[c]:[],[c]),onItemOpen:d,onItemClose:P.useCallback(()=>o&&d(""),[o,d]),children:a.jsx(Qe,{scope:e.__scopeAccordion,collapsible:o,children:a.jsx(ze,{...i,ref:t})})})}),Xt=P.forwardRef((e,t)=>{const{value:r,defaultValue:s,onValueChange:n=()=>{},...o}=e,[i,c]=re({prop:r,defaultProp:s??[],onChange:n,caller:R}),d=P.useCallback(p=>c((l=[])=>[...l,p]),[c]),u=P.useCallback(p=>c((l=[])=>l.filter(f=>f!==p)),[c]);return a.jsx(Te,{scope:e.__scopeAccordion,value:i,onItemOpen:d,onItemClose:u,children:a.jsx(Qe,{scope:e.__scopeAccordion,collapsible:!0,children:a.jsx(ze,{...o,ref:t})})})}),[Zt,V]=B(R),ze=P.forwardRef((e,t)=>{const{__scopeAccordion:r,disabled:s,dir:n,orientation:o="vertical",...i}=e,c=P.useRef(null),d=$e(c,t),u=Wt(r),l=$t(n)==="ltr",f=Ne(e.onKeyDown,m=>{if(!Ht.includes(m.key))return;const v=m.target,j=u().filter(E=>!E.ref.current?.disabled),N=j.findIndex(E=>E.ref.current===v),$=j.length;if(N===-1)return;m.preventDefault();let x=N;const S=0,C=$-1,U=()=>{x=N+1,x>C&&(x=S)},k=()=>{x=N-1,x<S&&(x=C)};switch(m.key){case"Home":x=S;break;case"End":x=C;break;case"ArrowRight":o==="horizontal"&&(l?U():k());break;case"ArrowDown":o==="vertical"&&U();break;case"ArrowLeft":o==="horizontal"&&(l?k():U());break;case"ArrowUp":o==="vertical"&&k();break}const X=x%$;j[X].ref.current?.focus()});return a.jsx(Zt,{scope:r,disabled:s,direction:n,orientation:o,children:a.jsx(de.Slot,{scope:r,children:a.jsx(T.div,{...i,"data-orientation":o,ref:d,onKeyDown:s?void 0:f})})})}),q="AccordionItem",[Yt,me]=B(q),Ge=P.forwardRef((e,t)=>{const{__scopeAccordion:r,value:s,...n}=e,o=V(q,r),i=Bt(q,r),c=ue(r),d=we(),u=s&&i.value.includes(s)||!1,p=o.disabled||e.disabled;return a.jsx(Yt,{scope:r,open:u,disabled:p,triggerId:d,children:a.jsx(qt,{"data-orientation":o.orientation,"data-state":Fe(u),...c,...n,ref:t,disabled:p,open:u,onOpenChange:l=>{l?i.onItemOpen(s):i.onItemClose(s)}})})});Ge.displayName=q;var qe="AccordionHeader",Oe=P.forwardRef((e,t)=>{const{__scopeAccordion:r,...s}=e,n=V(R,r),o=me(qe,r);return a.jsx(T.h3,{"data-orientation":n.orientation,"data-state":Fe(o.open),"data-disabled":o.disabled?"":void 0,...s,ref:t})});Oe.displayName=qe;var ee="AccordionTrigger",De=P.forwardRef((e,t)=>{const{__scopeAccordion:r,...s}=e,n=V(R,r),o=me(ee,r),i=Vt(ee,r),c=ue(r);return a.jsx(de.ItemSlot,{scope:r,children:a.jsx(Ot,{"aria-disabled":o.open&&!i.collapsible||void 0,"data-orientation":n.orientation,id:o.triggerId,...c,...s,ref:t})})});De.displayName=ee;var He="AccordionContent",We=P.forwardRef((e,t)=>{const{__scopeAccordion:r,...s}=e,n=V(R,r),o=me(He,r),i=ue(r);return a.jsx(Dt,{role:"region","aria-labelledby":o.triggerId,"data-orientation":n.orientation,...i,...s,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});We.displayName=He;function Fe(e){return e?"open":"closed"}var Jt=Me,er=Ge,tr=Oe,Be=De,Ve=We;/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rr=[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["line",{x1:"12",x2:"12",y1:"8",y2:"16",key:"10p56q"}],["line",{x1:"8",x2:"16",y1:"12",y2:"12",key:"1jonct"}]],sr=se("badge-plus",rr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ar=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Ke=se("chevron-down",ar);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nr=[["line",{x1:"21",x2:"14",y1:"4",y2:"4",key:"obuewd"}],["line",{x1:"10",x2:"3",y1:"4",y2:"4",key:"1q6298"}],["line",{x1:"21",x2:"12",y1:"12",y2:"12",key:"1iu8h1"}],["line",{x1:"8",x2:"3",y1:"12",y2:"12",key:"ntss68"}],["line",{x1:"21",x2:"16",y1:"20",y2:"20",key:"14d8ph"}],["line",{x1:"12",x2:"3",y1:"20",y2:"20",key:"m0wm8r"}],["line",{x1:"14",x2:"14",y1:"2",y2:"6",key:"14e1ph"}],["line",{x1:"8",x2:"8",y1:"10",y2:"14",key:"1i6ji0"}],["line",{x1:"16",x2:"16",y1:"18",y2:"22",key:"1lctlv"}]],or=se("sliders-horizontal",nr),ir=(e={})=>W(_.quest.getActive.queryOptions({...e})),cr=(e={})=>W(_.quest.getCompleted.queryOptions({...e})),lr=(e={})=>W(_.quest.getStoryQuests.queryOptions({...e}));function dr(){const{data:e,isLoading:t}=Mt({select:o=>o.slice(0,3)}),r=e?.filter(o=>o.questStatus==="complete"),[s,n]=y.useState("");return y.useEffect(()=>{n(fe());const o=setInterval(()=>{n(fe())},6e4);return()=>clearInterval(o)},[]),a.jsxs(Pt,{to:"/dailies",className:"flex items-center justify-between px-3 py-2 bg-purple-900/20 rounded-lg border border-purple-900/30",children:[a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx(Pe,{className:"size-4 text-purple-400"}),a.jsx("span",{className:"text-sm text-purple-300 font-medium font-display",children:"Daily Tasks"}),!t&&e?.length>0&&a.jsxs("span",{className:"text-sm text-purple-300 font-medium font-display",children:[r?.length," / ",e?.length]})]}),a.jsxs("span",{className:"text-xs text-gray-300",children:["Resets in ",s]})]})}var ur=new Set(["style"]),mr="use"in St,pr={srcset:"srcSet",fetchpriority:mr?"fetchPriority":"fetchpriority"},hr=e=>e.startsWith("data-")||e.startsWith("aria-")?e:pr[e]||e.replace(/-./g,t=>t[1].toUpperCase());function pe(e){return Object.fromEntries(Object.entries(e).map(([t,r])=>[hr(t),ur.has(t)&&r&&typeof r!="string"?pe(r):r]))}var Xe=(e,t)=>{if(!(!e||!t))switch(t){case"constrained":return`(min-width: ${e}px) ${e}px, 100vw`;case"fixed":return`${e}px`;case"fullWidth":return"100vw";default:return}},M=e=>e||e===0?`${e}px`:void 0,fr=({width:e,height:t,aspectRatio:r,layout:s,objectFit:n="cover",background:o})=>{const i=[["object-fit",n]];return o?.startsWith("https:")||o?.startsWith("http:")||o?.startsWith("data:")||o?.startsWith("/")?(i.push(["background-image",`url(${o})`]),i.push(["background-size","cover"]),i.push(["background-repeat","no-repeat"])):i.push(["background",o]),s==="fixed"&&(i.push(["width",M(e)]),i.push(["height",M(t)])),s==="constrained"&&(i.push(["max-width",M(e)]),i.push(["max-height",M(t)]),i.push(["aspect-ratio",r?`${r}`:void 0]),i.push(["width","100%"])),s==="fullWidth"&&(i.push(["width","100%"]),i.push(["aspect-ratio",r?`${r}`:void 0]),i.push(["height",M(t)])),Object.fromEntries(i.filter(([,c])=>c))},gr=[6016,5120,4480,3840,3200,2560,2048,1920,1668,1280,1080,960,828,750,640],ge=24,xr=({width:e,layout:t,resolutions:r=gr})=>{if(t==="fullWidth")return r;if(!e)return[];const s=e*2;return t==="fixed"?[e,s]:t==="constrained"?[e,s,...r.filter(n=>n<s)]:[]},yr=({src:e,width:t,layout:r="constrained",height:s,aspectRatio:n,breakpoints:o,format:i})=>(o||=xr({width:t,layout:r}),o.sort((c,d)=>c-d).map(c=>{let d;return s&&n&&(d=Math.round(c/n)),{url:e,width:c,height:d,format:i}})),Ze=e=>{let{src:t,transformer:r,operations:s}=e;return r?yr(e).map(({url:n,...o})=>`${r(t,{...s,...o},e.options)?.toString()} ${o.width}w`).join(`,
`):""};function Ye({width:e,height:t,priority:r,layout:s="constrained",aspectRatio:n,...o}){return e=e&&Number(e)||void 0,t=t&&Number(t)||void 0,r?(o.loading||="eager",o.fetchpriority||="high"):(o.loading||="lazy",o.decoding||="async"),o.alt===""&&(o.role||="presentation"),n?e?t||(t=Math.round(e/n)):t&&(e=Math.round(t*n)):e&&t&&(n=e/t),{width:e,height:t,aspectRatio:n,layout:s,...o}}function br(e){let{src:t,transformer:r,background:s,layout:n,objectFit:o,breakpoints:i,width:c,height:d,aspectRatio:u,unstyled:p,operations:l,options:f,...m}=Ye(e);if(r&&s==="auto"){const j=u?Math.round(ge/u):void 0,N=r(t,{width:ge,height:j},f);N&&(s=N.toString())}const v={width:c,height:d,aspectRatio:u,layout:n,objectFit:o,background:s};if(m.sizes||=Xe(c,n),p||(m.style={...fr(v),...m.style}),r){m.srcset=Ze({src:t,width:c,height:d,aspectRatio:u,layout:n,breakpoints:i,transformer:r,operations:l,options:f});const j=r(t,{...l,width:c,height:d},f);j&&(t=j),(n==="fullWidth"||n==="constrained")&&(c=void 0,d=void 0)}return{...m,src:t?.toString(),width:c,height:d}}function vr(e){return e?e.startsWith("image/")?{format:e.slice(6),mimeType:e}:{format:e,mimeType:`image/${e==="jpg"?"jpeg":e}`}:{}}function jr({media:e,type:t,...r}){let{src:s,transformer:n,layout:o,breakpoints:i,width:c,height:d,aspectRatio:u,sizes:p,loading:l,decoding:f,operations:m,options:v,...j}=Ye(r);if(!n)return{};const{format:N,mimeType:$}=vr(t);p||=Xe(c,o);const x=Ze({src:s,width:c,height:d,aspectRatio:u,layout:o,breakpoints:i,transformer:n,format:N,operations:m,options:v}),S=n(s,{...m,width:c,height:d},v);S&&(s=S);const C={...j,sizes:p,srcset:x};return e&&(C.media=e),$&&(C.type=$),C}const wr={"images.ctfassets.net":"contentful","cdn.builder.io":"builder.io","images.prismic.io":"imgix","www.datocms-assets.com":"imgix","cdn.sanity.io":"imgix","images.unsplash.com":"imgix","cdn.shopify.com":"shopify","s7d1.scene7.com":"scene7","ip.keycdn.com":"keycdn","assets.caisy.io":"bunny","images.contentstack.io":"contentstack","ucarecdn.com":"uploadcare","imagedelivery.net":"cloudflare_images"},Nr={"imgix.net":"imgix","wp.com":"wordpress","files.wordpress.com":"wordpress","b-cdn.net":"bunny","storyblok.com":"storyblok","kc-usercontent.com":"kontent.ai","cloudinary.com":"cloudinary","kxcdn.com":"keycdn","imgeng.in":"imageengine","imagekit.io":"imagekit","cloudimg.io":"cloudimage","ucarecdn.com":"uploadcare","supabase.co":"supabase","graphassets.com":"hygraph"},$r={"/cdn-cgi/image/":"cloudflare","/cdn-cgi/imagedelivery/":"cloudflare_images","/_next/image":"nextjs","/_vercel/image":"vercel","/is/image":"scene7","/_ipx/":"ipx","/_image":"astro","/.netlify/images":"netlify","/storage/v1/object/public/":"supabase","/storage/v1/render/image/public/":"supabase","/v1/storage/buckets/":"appwrite"};function O(e){if(!e)return e;const t=Number(e);return isNaN(t)?e:Math.round(t)}const Pr=e=>{const{pathname:t,search:r}=e;return`${t}${r}`},g=e=>e.hostname==="n"?Pr(e):e.toString(),h=(e,t)=>typeof e=="string"?new URL(e,"http://n/"):e,xe=e=>e===" "?"+":"%"+e.charCodeAt(0).toString(16).toUpperCase().padStart(2,"0"),Je=e=>e?.startsWith("/")?e.slice(1):e,et=e=>e?.endsWith("/")?e.slice(0,-1):e,Sr=e=>e?.endsWith("/")?e:`${e}/`,_r=(e,t)=>{const r=xe(e),s=xe(t);function n(i){return encodeURIComponent(i).replaceAll(e,r).replaceAll(t,s)}function o(i,c){return`${n(i)}${e}${n(String(c))}`}return i=>(Array.isArray(i)?i:Object.entries(i)).flatMap(([d,u])=>u==null?[]:Array.isArray(u)?u.map(p=>o(d,p)):o(d,u)).join(t)},Ir=(e,t)=>e==="="&&t==="&"?kr:r=>{const s=r.toString();return Object.fromEntries(s.split(t).map(n=>{const[o,i]=n.split(e);return[decodeURI(o),decodeURI(i)]}))};function Rr(e,t=4e3,r=4e3){let{width:s,height:n}=e;return s=Number(s)||void 0,n=Number(n)||void 0,s&&s>t&&(n&&(n=Math.round(n*t/s)),s=t),n&&n>r&&(s&&(s=Math.round(s*r/n)),n=r),{width:s,height:n}}function Q(e){const t=h(e),r=Object.fromEntries(t.searchParams.entries());for(const s in["width","height","quality"]){const n=r[s];if(n){const o=Number(n);isNaN(o)||(r[s]=o)}}return t.search="",{operations:r,src:g(t)}}function tt({keyMap:e={},formatMap:t={},defaults:r={}},s){s.format&&s.format in t&&(s.format=t[s.format]),s.width&&(s.width=O(s.width)),s.height&&(s.height=O(s.height));for(const n in e){if(!Object.prototype.hasOwnProperty.call(e,n))continue;const o=n;if(e[o]===!1){delete s[o];continue}e[o]&&s[o]&&(s[e[o]]=s[o],delete s[o])}for(const n in r){if(!Object.prototype.hasOwnProperty.call(r,n))continue;const o=n,i=r[o];if(!s[o]&&i!==void 0){if(e[o]===!1)continue;const c=e[o]??o;if(c in s)continue;s[c]=i}}return s}const ye=e=>Object.fromEntries(Object.entries(e).map(([t,r])=>[r,t]));function Cr({keyMap:e={},formatMap:t={},defaults:r={}},s){const n=ye(e),o=ye(t),i=tt({keyMap:n,formatMap:o,defaults:r},s);i.width&&(i.width=O(i.width)),i.height&&(i.height=O(i.height));const c=Number(i.quality);return isNaN(c)||(i.quality=c),i}const kr=e=>{const t=h(e);return Object.fromEntries(t.searchParams.entries())};function A({kvSeparator:e="=",paramSeparator:t="&",...r}={}){const s=_r(e,t);return n=>{const o=tt(r,n);return s(o)}}function Ar({kvSeparator:e="=",paramSeparator:t="&",defaults:r,...s}={}){const n=Ir(e,t);return o=>{const i=o?n(o):{};return Cr(s,i)}}function w(e){const t=A(e),r=Ar(e);return{operationsGenerator:t,operationsParser:r}}function rt(e){if(e!=null)try{return!!JSON.parse(e?.toString())}catch{return!!e}}const Ur=e=>Object.fromEntries(Object.entries(e).filter(([,t])=>t!==void 0));function b(e,t){return(r,s,n)=>{const o=e(r,n);return o?t(o.src,{...o.operations,...Ur(s)},{...o.options,...n}):t(r,s,n)}}const Er=new Map(Object.entries(wr)),Lr=Object.entries(Nr),Mr=Object.entries($r);function K(e){return Tr(e)||z(e)}function Tr(e){if(typeof e=="string"&&!e.startsWith("https://"))return!1;const{hostname:t}=h(e),r=Er.get(t);return r||Lr.find(([s])=>t.endsWith(s))?.[1]||!1}function z(e){const{pathname:t}=h(e);return Mr.find(([r])=>t.startsWith(r))?.[1]||!1}const Qr="/view?",zr="/preview?",{operationsGenerator:Gr,operationsParser:qr}=w({keyMap:{format:"output"},kvSeparator:"=",paramSeparator:"&"}),Or=(e,t)=>{const r=h(e.toString().replace(Qr,zr)),s=r.searchParams.get("project")??"",n=Gr(t);return r.search=n,r.searchParams.append("project",s),g(r)},Dr=e=>{if(z(e)!=="appwrite")return null;const t=h(e),r=qr(t);delete r.project;const s=t.searchParams.get("project")??"";return t.search="",t.searchParams.append("project",s),{src:t.href,operations:r}},Hr=b(Dr,Or),st="/_image",{operationsParser:Wr,operationsGenerator:Fr}=w({keyMap:{format:"f",width:"w",height:"h",quality:"q"},defaults:{fit:"cover"}}),Z=(e,t,r)=>{const s=h(`${et(r?.baseUrl??"")}${r?.endpoint??st}`),n=Fr(t);return s.search=n,s.searchParams.set("href",e.toString()),g(s)},Br=e=>{const t=h(e),r=t.searchParams.get("href");if(!r)return null;t.searchParams.delete("href");const s=Wr(t);return{src:r,operations:s,options:{baseUrl:t.origin}}},Vr=(e,t,r={})=>{if(h(e).pathname!==(r?.endpoint??st))return Z(e,t,r);const n=Br(e);return n?(r.baseUrl??=n.options.baseUrl,Z(n.src,{...n.operations,...t},r)):Z(e,t,r)},Kr=A({defaults:{fit:"cover",format:"webp",sharp:!0}}),Xr=Q,Zr=(e,t)=>{const r=Kr(t),s=h(e);return s.search=r,g(s)},Yr=b(Xr,Zr),Jr=A({keyMap:{format:"output"}}),es=Q,ts=(e,t)=>{const r=Jr(t),s=h(e);return s.search=r,g(s)},rs=b(es,ts),ss=(e,t)=>{const{width:r,height:s}=t;return r&&s&&(t.aspect_ratio??=`${Math.round(Number(r))}:${Math.round(Number(s))}`),rs(e,t)},{operationsGenerator:as,operationsParser:ns}=w({keyMap:{format:"f"},defaults:{format:"auto",fit:"cover"},formatMap:{jpg:"jpeg"},kvSeparator:"=",paramSeparator:","}),os=(e,t,r)=>{const s=as(t),n=h(r?.domain?`https://${r.domain}`:"/");return n.pathname=`/cdn-cgi/image/${s}/${Je(e.toString())}`,g(n)},is=(e,t)=>{if(z(e)!=="cloudflare")return null;const r=h(e),[,,,s,...n]=r.pathname.split("/"),o=ns(s);return{src:g(h(n.join("/"))),operations:o,options:{domain:t?.domain??(r.hostname==="n"?void 0:r.hostname)}}},cs=b(is,os),ls=/https?:\/\/(?<host>[^\/]+)\/cdn-cgi\/imagedelivery\/(?<accountHash>[^\/]+)\/(?<imageId>[^\/]+)\/*(?<transformations>[^\/]+)*$/g,ds=/https?:\/\/(?<host>imagedelivery.net)\/(?<accountHash>[^\/]+)\/(?<imageId>[^\/]+)\/*(?<transformations>[^\/]+)*$/g,{operationsGenerator:us,operationsParser:ms}=w({keyMap:{width:"w",height:"h",format:"f"},defaults:{fit:"cover"},kvSeparator:"=",paramSeparator:","});function at(e,t){const{host:r,accountHash:s,imageId:n}=e;if(!r||!s||!n)throw new Error("Missing required Cloudflare Images options");return["https:/",...r==="imagedelivery.net"?[r]:[r,"cdn-cgi","imagedelivery"],s,n,t].filter(Boolean).join("/")}const ps=(e,t,r={})=>{const s=us(t),n=at(r,s);return g(h(n))},hs=e=>{const t=h(e),r=[...t.toString().matchAll(ls),...t.toString().matchAll(ds)];if(!r[0]?.groups)return null;const{host:s,accountHash:n,imageId:o,transformations:i}=r[0].groups,c=ms(i||""),d={host:s,accountHash:n,imageId:o};return{src:at(d),operations:c,options:d}},fs=(e,t,r={})=>{const s=hs(e);if(!s)throw new Error("Invalid Cloudflare Images URL");const n={...s.operations,...t};return ps(s.src,n,{...s.options,...r})},{operationsGenerator:gs,operationsParser:xs}=w({keyMap:{format:"force_format",width:"w",height:"h",quality:"q"},defaults:{org_if_sml:1}}),ys=(e,t={},{token:r}={})=>{if(!r)throw new Error("Token is required for Cloudimage URLs"+e);let s=e.toString();s=s.replace(/^https?:\/\//,""),s.includes("?")&&(t.ci_url_encoded=1,s=encodeURIComponent(s));const n=gs(t),o=new URL(`https://${r}.cloudimg.io/`);return o.pathname=s,o.search=n,o.toString()},bs=(e,t={})=>{const r=h(e);if(K(r)!=="cloudimage")return null;const s=xs(r);let n=r.pathname;return s.ci_url_encoded&&(n=decodeURIComponent(n),delete s.ci_url_encoded),t.token??=r.hostname.replace(".cloudimg.io",""),{src:`${r.protocol}/${n}`,operations:s,options:t}},vs=b(bs,ys),js=/https?:\/\/(?<host>res\.cloudinary\.com)\/(?<cloudName>[a-zA-Z0-9-]+)\/(?<assetType>image|video|raw)\/(?<deliveryType>upload|fetch|private|authenticated|sprite|facebook|twitter|youtube|vimeo)\/?(?<signature>s\-\-[a-zA-Z0-9]+\-\-)?\/?(?<transformations>(?:[^_\/]+_[^,\/]+,?)*)?\/(?:(?<version>v\d+)\/)?(?<id>(?:[^\s\/]+\/)*[^\s\/]+(?:\.[a-zA-Z0-9]+)?)$/,ws=/https?:\/\/(?<host>(?<cloudName>[a-zA-Z0-9-]+)-res\.cloudinary\.com|[a-zA-Z0-9.-]+)\/(?<assetType>image|video|raw)\/(?<deliveryType>upload|fetch|private|authenticated|sprite|facebook|twitter|youtube|vimeo)\/?(?<signature>s\-\-[a-zA-Z0-9]+\-\-)?\/?(?<transformations>(?:[^_\/]+_[^,\/]+,?)*)?\/(?:(?<version>v\d+)\/)?(?<id>(?:[^\s\/]+\/)*[^\s\/]+(?:\.[a-zA-Z0-9]+)?)$/,{operationsGenerator:Ns,operationsParser:$s}=w({keyMap:{width:"w",height:"h",format:"f",quality:"q"},defaults:{format:"auto",c:"lfill"},kvSeparator:"_",paramSeparator:","});function Ps({host:e,cloudName:t,assetType:r,deliveryType:s,signature:n,transformations:o,version:i,id:c}){return["https:/",e,e==="res.cloudinary.com"?t:void 0,r,s,n,o,i,c].filter(Boolean).join("/")}function Ss(e){let t=e.toString().match(js);return t?.length||(t=e.toString().match(ws)),t?.length?t.groups||{}:null}const _s=(e,t)=>{const r=Ss(e.toString());if(!r)return e.toString();const s=$s(r.transformations||"");return r.transformations=Ns({...s,...t}),Ps(r)},Is=A({keyMap:{format:"fm",width:"w",height:"h",quality:"q"},defaults:{fit:"fill"}}),Rs=(e,t)=>{const r=Is(t),s=new URL(e);return s.search=r,g(s)},Cs=Q,ks=b(Cs,Rs),As=(e,t)=>{const{width:r,height:s}=Rr(t,4e3,4e3);return ks(e,{...t,width:r,height:s})},Us=A({defaults:{auto:"webp",disable:"upscale"}}),Es=(e,t,{baseURL:r="https://images.contentstack.io/"}={})=>{t.width&&t.height&&(t.fit??="crop");const s=Us(t),n=h(e);return n.hostname==="n"&&(n.protocol="https:",n.hostname=new URL(r).hostname),n.search=s,g(n)},Ls=e=>{const{src:t,operations:r}=Q(e)??{};if(!r||!t)return null;const{origin:s}=h(e);return{src:t,operations:r,options:{baseURL:s}}},Ms=b(Ls,Es),Ts=A({defaults:{withoutEnlargement:!0,fit:"cover"}}),Qs=(e,t)=>{Array.isArray(t.transforms)&&(t.transforms=JSON.stringify(t.transforms));const r=Ts(t),s=h(e);return s.search=r,g(s)},zs=e=>{const t=Q(e);if(t?.operations?.transforms&&typeof t.operations.transforms=="string")try{t.operations.transforms=JSON.parse(t.operations.transforms)}catch{return null}return t},Gs=b(zs,Qs),qs=/https:\/\/(?<region>[a-z0-9-]+)\.graphassets\.com\/(?<envId>[a-zA-Z0-9]+)(?:\/(?<transformations>.*?))?\/(?<handle>[a-zA-Z0-9]+)$/;w({keyMap:{width:"width",height:"height",format:"format"},defaults:{format:"auto",fit:"crop"}});const nt=e=>{const r=h(e).toString().match(qs);if(!r?.groups)return null;const{region:s,envId:n,handle:o,transformations:i}=r.groups,c={};return i&&i.split("/").forEach(u=>{const[p,l]=u.split("=");p==="resize"&&l?l.split(",").forEach(f=>{const[m,v]=f.split(":");m==="width"||m==="height"?c[m]=Number(v):m==="fit"&&(c.fit=v)}):p==="output"&&l?l.split(",").forEach(f=>{const[m,v]=f.split(":");m==="format"&&(c.format=v)}):p==="auto_image"&&(c.format="auto")}),{src:`https://${s}.graphassets.com/${n}/${o}`,operations:c,options:{region:s,envId:n,handle:o}}},Os=(e,t,r={})=>{const s=nt(e);if(!s)throw new Error("Invalid Hygraph URL");const{region:n,envId:o,handle:i}={...s.options,...r},c=[];if(t.width||t.height){const l=[];t.width&&t.height?l.push("fit:crop"):t.fit&&l.push(`fit:${t.fit}`),t.width&&l.push(`width:${t.width}`),t.height&&l.push(`height:${t.height}`),l.length&&c.push(`resize=${l.join(",")}`)}t.format==="auto"||!t.format&&!s.operations.format?c.push("auto_image"):t.format&&c.push(`output=format:${t.format}`);const d=`https://${n}.graphassets.com/${o}`,u=c.length>0?"/"+c.join("/"):"",p=h(`${d}${u}/${i}`);return g(p)},Ds=b(nt,Os),{operationsGenerator:Hs,operationsParser:Ws}=w({keyMap:{width:"w",height:"h",format:"f"},defaults:{m:"cropbox"},kvSeparator:"_",paramSeparator:"/"}),Fs=(e,t)=>{const r=Hs(t),s=h(e);return s.searchParams.set("imgeng",r),g(s)},Bs=e=>{const t=h(e),r=t.searchParams.get("imgeng");if(!r)return null;const s=Ws(r);return t.searchParams.delete("imgeng"),{src:g(t),operations:s}},Vs=b(Bs,Fs),{operationsGenerator:Ks,operationsParser:Xs}=w({keyMap:{width:"w",height:"h",format:"f",quality:"q"},defaults:{c:"maintain_ratio",fo:"auto"},kvSeparator:"-",paramSeparator:","}),Zs=(e,t)=>{const r=Ks(t),s=h(e);return s.searchParams.set("tr",r),g(s)},Ys=e=>{const t=h(e);let r=null,s=t.pathname;if(t.searchParams.has("tr"))r=t.searchParams.get("tr"),t.searchParams.delete("tr");else{const o=t.pathname.split("/"),i=o.findIndex(c=>c.startsWith("tr:"));i!==-1&&(r=o[i].slice(3),s=o.slice(0,i).concat(o.slice(i+1)).join("/"))}if(!r)return null;t.pathname=s;const n=Xs(r);return{src:g(t),operations:n}},Js=b(Ys,Zs),{operationsGenerator:ea,operationsParser:ta}=w({keyMap:{format:"fm",width:"w",height:"h",quality:"q"},defaults:{fit:"min",auto:"format"}}),ra=e=>{const t=h(e),r=ta(e);return t.search="",{src:g(t),operations:r}},sa=(e,t)=>{const r=ea(t),s=h(e);return s.search=r,s.searchParams.has("fm")&&s.searchParams.get("auto")==="format"&&s.searchParams.delete("auto"),g(s)},aa=b(ra,sa),{operationsGenerator:na,operationsParser:oa}=w({keyMap:{width:"w",height:"h",quality:"q",format:"f"},defaults:{f:"auto"},kvSeparator:"_",paramSeparator:","}),be=(e,t,r)=>{t.width&&t.height&&(t.s=`${t.width}x${t.height}`,delete t.width,delete t.height);const s=na(t),n=r?.baseURL??"/_ipx",o=h(n);return o.pathname=`${o.pathname}/${s}/${Je(e.toString())}`,g(o)},ia=e=>{const t=h(e),[,r,s,...n]=t.pathname.split("/");if(!s||!n.length)return null;const o=oa(s);if(o.s){const[i,c]=o.s.split("x").map(Number);o.width=i,o.height=c,delete o.s}return{src:"/"+n.join("/"),operations:o,options:{baseURL:`${t.origin}/${r}`}}},ca=(e,t,r)=>{const s=h(e),n=r?.baseURL;if(n&&s.toString().startsWith(n)||s.pathname.startsWith("/_ipx")){const o=ia(e);if(o)return be(o.src,{...o.operations,...t},{baseURL:o.options.baseURL})}return be(e,t,{baseURL:n})},ot=["enlarge","flip","flop","negate","normalize","grayscale","removealpha","olrepeat","progressive","adaptive","lossless","nearlossless","metadata"],{operationsGenerator:la,operationsParser:da}=w({defaults:{fit:"cover"},formatMap:{jpg:"jpeg"}}),ua=(e,t)=>{const r=h(e);for(const s of ot)t[s]!==void 0&&(t[s]=t[s]?1:0);return r.search=la(t),g(r)},ma=e=>{const t=h(e),r=da(t);for(const s of ot)r[s]!==void 0&&(r[s]=rt(r[s]));return t.search="",{src:g(t),operations:r}},pa=b(ma,ua),{operationsGenerator:ha,operationsParser:fa}=w({formatMap:{jpg:"jpeg"},keyMap:{format:"fm",width:"w",height:"h",quality:"q"}}),ga=(e,t)=>{const r=h(e);return t.lossless!==void 0&&(t.lossless=t.lossless?1:0),t.width&&t.height&&(t.fit="crop"),r.search=ha(t),g(r)},xa=e=>{const t=h(e),r=fa(t);return r.lossless!==void 0&&(r.lossless=rt(r.lossless)),t.search="",{src:g(t),operations:r}},ya=b(xa,ga),{operationsGenerator:ba,operationsParser:va}=w({defaults:{fit:"cover"},keyMap:{format:"fm",width:"w",height:"h",quality:"q"}}),ja=(e,t,r={})=>{const s=h(`${r.baseUrl||""}/.netlify/images`);return s.search=ba(t),s.searchParams.set("url",e.toString()),g(s)},wa=e=>{if(z(e)!=="netlify")return null;const t=h(e),r=va(t);delete r.url;const s=t.searchParams.get("url")||"";return t.search="",{src:s,operations:r,options:{baseUrl:t.hostname==="n"?void 0:t.origin}}},Na=b(wa,ja),{operationsGenerator:$a,operationsParser:Pa}=w({keyMap:{width:"w",quality:"q",height:!1,format:!1},defaults:{q:75}}),it=(e,t,r={})=>{const s=h(`${r.baseUrl||""}/${r.prefix||"_vercel"}/image`);return s.search=$a(t),s.searchParams.append("url",e.toString()),g(s)},ct=(e,t={})=>{if(!["vercel","nextjs"].includes(z(e)||""))return null;const r=h(e),s=r.searchParams.get("url")||"";r.searchParams.delete("url");const n=Pa(r);return r.search="",{src:s,operations:n,options:{baseUrl:t.baseUrl??r.origin}}},Sa=b(ct,it),_a=(e,t,r={})=>it(e,t,{...r,prefix:"_next"}),Ia=(e,t)=>ct(e,t),Ra=b(Ia,_a),{operationsGenerator:Ca,operationsParser:ka}=w({keyMap:{width:"wid",height:"hei",quality:"qlt",format:"fmt"},defaults:{fit:"crop,0"}}),lt="https://s7d1.scene7.com/is/image/",Aa=(e,t)=>{const r=new URL(e,lt);return r.search=Ca(t),g(r)},Ua=e=>{if(K(e)!=="scene7")return null;const t=new URL(e,lt),r=ka(t);return t.search="",{src:t.toString(),operations:r}},Ea=b(Ua,Aa),te=/(.+?)(?:_(?:(pico|icon|thumb|small|compact|medium|large|grande|original|master)|(\d*)x(\d*)))?(?:_crop_([a-z]+))?(\.[a-zA-Z]+)(\.png|\.jpg|\.webp|\.avif)?$/,{operationsGenerator:La,operationsParser:Ma}=w({keyMap:{format:!1}}),Ta=(e,t)=>{const r=h(e),s=r.pathname.replace(te,"$1$6");return r.pathname=s,r.search=La(t),g(r)},Qa=e=>{const t=h(e),r=te.exec(t.pathname),s=Ma(t);if(r){const[,,,o,i,c]=r;o&&i&&!s.width&&!s.height&&(s.width=parseInt(o,10),s.height=parseInt(i,10)),c&&(s.crop??=c)}const n=t.pathname.replace(te,"$1$6");t.pathname=n;for(const o of["width","height","crop","pad_color","format"])t.searchParams.delete(o);return{src:t.toString(),operations:s}},za=b(Qa,Ta),Ga=/(?<id>\/f\/\d+\/\d+x\d+\/\w+\/[^\/]+)\/?(?<modifiers>m\/?(?<crop>\d+x\d+:\d+x\d+)?\/?(?<resize>(?<flipx>\-)?(?<width>\d+)x(?<flipy>\-)?(?<height>\d+))?\/?(filters\:(?<filters>[^\/]+))?)?$/,qa=/^(?<modifiers>\/(?<crop>\d+x\d+:\d+x\d+)?\/?(?<resize>(?<flipx>\-)?(?<width>\d+)x(?<flipy>\-)?(?<height>\d+))?\/?(filters\:(?<filters>[^\/]+))?\/?)?(?<id>\/f\/.+)$/,Oa=e=>e?Object.fromEntries(e.split(":").map(t=>{if(!t)return[];const[r,s]=t.split("(");return[r,s.replace(")","")]})):{},Da=e=>{if(!e)return;const t=Object.entries(e).map(([r,s])=>`${r}(${s??""})`);if(t.length!==0)return`filters:${t.join(":")}`},Ha=e=>{const t=h(e),s=(t.hostname==="img2.storyblok.com"?qa:Ga).exec(t.pathname);if(!s||!s.groups)return null;const{id:n,crop:o,width:i,height:c,filters:d,flipx:u,flipy:p}=s.groups,{format:l,...f}=Oa(d??"");t.hostname==="img2.storyblok.com"&&(t.hostname="a.storyblok.com");const m=Object.fromEntries([["width",Number(i)||void 0],["height",Number(c)||void 0],["format",l],["crop",o],["filters",f],["flipx",u],["flipy",p]].filter(([v,j])=>j!==void 0));return{src:`${t.origin}${n}`,operations:m}},Wa=(e,t)=>{const r=h(e),{width:s=0,height:n=0,format:o,crop:i,filters:c={},flipx:d="",flipy:u=""}=t,p=`${d}${s}x${u}${n}`;o&&(c.format=o);const l=[r.pathname,"m",i,p,Da(c)].filter(Boolean);return r.pathname=l.join("/"),g(r)},Fa=b(Ha,Wa),D="/storage/v1/object/public/",H="/storage/v1/render/image/public/",Ba=e=>e.pathname.startsWith(H),{operationsGenerator:Va,operationsParser:Ka}=w({}),Xa=(e,t)=>{const r=h(e),s=r.pathname.replace(H,D);return r.pathname=s,t.format&&t.format!=="origin"&&delete t.format,r.search=Va(t),g(r).replace(D,H)},Za=e=>{const t=h(e),r=Ka(t),s=Ba(t),n=t.pathname.replace(H,"").replace(D,"");return s?{src:`${t.origin}${D}${n}`,operations:r}:{src:g(t),operations:r}},Ya=b(Za,Xa),dt=/^https?:\/\/(?<host>[^\/]+)\/(?<uuid>[^\/]+)(?:\/(?<filename>[^\/]+)?)?/,{operationsGenerator:Ja,operationsParser:en}=w({keyMap:{width:!1,height:!1},defaults:{format:"auto"},kvSeparator:"/",paramSeparator:"/-/"}),tn=e=>{const t=h(e),r=dt.exec(t.toString());if(!r||!r.groups)return null;const{host:s,uuid:n}=r.groups,[,...o]=t.pathname.split("/-/"),i=en(o.join("/-/")||"");if(i.resize){const[c,d]=i.resize.split("x");c&&(i.width=parseInt(c)),d&&(i.height=parseInt(d)),delete i.resize}return{src:`https://${s}/${n}/`,operations:i,options:{host:s}}},rn=(e,t,r={})=>{const s=h(e),n=r.host||s.hostname,o=dt.exec(s.toString());o?.groups&&(s.pathname=`/${o.groups.uuid}/`),t.resize=t.resize||`${t.width??""}x${t.height??""}`,delete t.width,delete t.height;const i=Sr(Ja(t));return s.hostname=n,s.pathname=et(s.pathname)+(i?`/-/${i}`:"")+(o?.groups?.filename??""),g(s)},sn=b(tn,rn),{operationsGenerator:an,operationsParser:nn}=w({keyMap:{width:"w",height:"h"},defaults:{crop:"1"}}),on=(e,t)=>{const r=h(e),{crop:s}=t;return typeof s<"u"&&s!=="0"&&(t.crop=s?"1":"0"),r.search=an(t),g(r)},cn=e=>{const t=h(e),r=nn(t);return r.crop!==void 0&&(r.crop=r.crop==="1"),t.search="",{src:g(t),operations:r}},ln=b(cn,on),dn={appwrite:Hr,astro:Vr,"builder.io":Yr,bunny:ss,cloudflare:cs,cloudflare_images:fs,cloudimage:vs,cloudinary:_s,contentful:As,contentstack:Ms,directus:Gs,hygraph:Ds,imageengine:Vs,imagekit:Js,imgix:aa,ipx:ca,keycdn:pa,"kontent.ai":ya,netlify:Na,nextjs:Ra,scene7:Ea,shopify:za,storyblok:Fa,supabase:Ya,uploadcare:sn,vercel:Sa,wordpress:ln};function ut(e){if(e)return dn[e]}function un({cdn:e,fallback:t,operations:r={},options:s,...n}){if(e??=K(n.src)||t,!e)return n;const o=ut(e);return o?br({...n,operations:r?.[e],options:s?.[e],transformer:o}):n}function mn({cdn:e,fallback:t,operations:r,options:s,...n}){if(e??=K(n.src)||t,!e)return n;const o=ut(e);return o?jr({...n,operations:r?.[e],options:s?.[e],transformer:o}):n}var pn=y.forwardRef(function(t,r){const s=pe(un(t));return a.jsx("img",{...s,ref:r})});y.forwardRef(function(t,r){const s=pe(mn(t));return a.jsx("source",{...s,ref:r})});const hn="https://cloudflare-image.jamessut.workers.dev",fn=e=>e?.avatar?e?.avatar.startsWith("http")||e?.avatar.startsWith("https")?e.avatar:`${hn}${e.avatar}`:Se,gn=({src:e,className:t,width:r=100,height:s=100,...n})=>{const o=fn(e);return a.jsx(pn,{src:o,className:I(t||"rounded-md object-cover"),alt:`${e?.name}'s avatar`,width:r,height:s,layout:"constrained",onError:i=>{i.target.onerror=null,i.target.src=Se},...n})},xn=Jt,mt=y.forwardRef(({className:e,...t},r)=>a.jsx(er,{ref:r,className:I("border-b",e),...t}));mt.displayName="AccordionItem";const pt=y.forwardRef(({className:e,children:t,...r},s)=>a.jsx(tr,{className:"flex",children:a.jsxs(Be,{ref:s,className:I("flex flex-1 items-center justify-between py-4 text-sm font-medium transition-all hover:underline text-left [&[data-state=open]>svg]:rotate-180",e),...r,children:[t,a.jsx(Ke,{className:"size-4 shrink-0 text-zinc-500 transition-transform duration-200 dark:text-zinc-400"})]})}));pt.displayName=Be.displayName;const ht=y.forwardRef(({className:e,children:t,...r},s)=>a.jsx(Ve,{ref:s,className:"overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...r,children:a.jsx("div",{className:I("pb-2 pt-0",e),children:t})}));ht.displayName=Ve.displayName;function yn(){const e=ae();return ne(_.quest.complete.mutationOptions({onSuccess:()=>{e.invalidateQueries({queryKey:_.quest.getCompleted.key()}),e.invalidateQueries({queryKey:_.quest.getActive.key()}),e.invalidateQueries({queryKey:[_e.USER.CURRENTUSERINFO]})},onError:t=>{console.error("Failed to complete quest:",t)}}))}function bn(){const e=ae();return ne(_.quest.start.mutationOptions({onSuccess:()=>{e.invalidateQueries({queryKey:_.quest.getAvailable.key()}),e.invalidateQueries({queryKey:_.quest.getActive.key()})},onError:t=>{console.error("Failed to start quest:",t)}}))}const G=e=>!e.quest_progress||e.quest_progress.length===0?"available":e.quest_progress[0].questStatus;function vn(e=[]){const[t,r]=y.useState("level"),[s,n]=y.useState("asc"),[o,i]=y.useState({status:"all"});return{filteredAndSortedQuests:y.useMemo(()=>!e||e.length===0?[]:e.filter(u=>!(o.status&&o.status!=="all"&&G(u)!==o.status||o.minLevel!==void 0&&u.levelReq<o.minLevel||o.maxLevel!==void 0&&u.levelReq>o.maxLevel||o.questGiverId!==void 0&&u.shopId!==o.questGiverId||o.hasItemRewards===!0&&!u.quest_reward.some(l=>l.rewardType==="ITEM")||o.hasTalentPointRewards===!0&&!(u.talentPointReward>0)||o.location!==void 0&&o.location!=="any"&&!u.quest_objective.some(l=>l.location?.toLowerCase()===o.location)||o.isStoryQuest!==void 0&&u.isStoryQuest!==o.isStoryQuest||o.chapterId!==void 0&&u.chapterId!==o.chapterId)).sort((u,p)=>{let l=0;switch(t){case"level":l=u.levelReq-p.levelReq;break;case"name":l=u.name.localeCompare(p.name);break;case"quest_giver":l=(u.shopId||0)-(p.shopId||0);break;case"Yen_reward":{const f=u.cashReward||0;l=(p.cashReward||0)-f;break}case"XP_reward":{const f=u.xpReward||0;l=(p.xpReward||0)-f;break}case"REP_reward":{const f=u.repReward||0;l=(p.repReward||0)-f;break}}return s==="asc"?l:-l}),[e,t,s,o]),sortOption:t,setSortOption:r,sortDirection:s,setSortDirection:n,filterOptions:o,setFilterOptions:i}}const jn=()=>{const e=ae();return ne(_.quest.handInItem.mutationOptions({onSuccess:()=>{e.invalidateQueries({queryKey:_.quest.getActive.key()}),e.invalidateQueries({queryKey:_e.USER.INVENTORY})}}))},ft=(e,t)=>{if(!e.quest_objective_progress||e.quest_objective_progress.length===0)return 0;if(e.objectiveType===J.ACQUIRE_ITEM){const r=e.quest_objective_progress.reduce((n,o)=>n+o.count,0);if(r>0)return r;const s=Re(t,e.itemId||0);return Math.min(s,e.quantity||0)}return e.quest_objective_progress.reduce((r,s)=>r+s.count,0)};function wn({objective:e,status:t}){const{mutate:r,isPending:s}=jn(),{data:n}=Ce(),o=l=>!n||!l.item||!l.itemId?!1:Re(n,l.itemId)>=(l.quantity||1),i=l=>{console.log(l),l.itemId&&r({objectiveId:l.id,itemId:l.itemId})},c=t==="complete"?e.quantity:ft(e,n||[]),d=e.quantity>0?c/e.quantity*100:0,p=e.objectiveType===J.DELIVER_ITEM&&t==="in_progress"&&o(e)&&c<(e.quantity||0);return a.jsxs("div",{className:"space-y-1 gap-6 flex items-center",children:[a.jsxs("div",{className:"flex-1",children:[a.jsxs("div",{className:"flex justify-between text-xs",children:[a.jsx("span",{className:"text-gray-100",children:Ie(e)}),t!=="available"&&a.jsxs("span",{className:"text-gray-300",children:[a.jsx("span",{className:"font-accent",children:c}),"/",a.jsx("span",{className:"font-accent",children:e.quantity})]})]}),t!=="available"&&a.jsx("div",{className:"w-full h-2 mt-0.5 bg-gray-800 rounded-xs overflow-hidden",children:a.jsx("div",{style:{width:`${d}%`},className:I("h-full rounded-xs",t==="in_progress"?"bg-blue-600":d>=100?"bg-green-600":"bg-blue-600")})})]}),e.objectiveType===J.DELIVER_ITEM&&a.jsx("button",{disabled:!p||s,className:"px-3 mr-2 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed",onClick:()=>i(e),children:s?"Handing In...":"Hand In Items"})]},e.id)}function Nn({quest:e,objectives:t,overallProgress:r}){const{mutate:s}=bn(),{mutate:n}=yn(),o=G(e);return a.jsxs("div",{className:"px-3 pb-3 border-t border-gray-700 pt-2",children:[a.jsx("div",{className:"bg-gray-900/30 rounded-lg p-3 mb-3 border border-gray-800",children:a.jsx("p",{className:"text-xs text-gray-200",children:e.description})}),a.jsxs("div",{className:"bg-gray-900/60 rounded-lg p-3 mb-3 border border-gray-800/80",children:[a.jsxs("div",{className:"flex items-start gap-2 mb-2",children:[a.jsx(_t,{className:"size-4 text-blue-400 mt-0.5"}),a.jsx("div",{children:a.jsx("h5",{className:"text-sm text-custom-yellow font-medium font-display",children:t.length>1?"Objectives":"Objective"})})]}),a.jsx("div",{className:"space-y-3 mt-2",children:t.map(i=>a.jsx(wn,{objective:i,status:o},i.id))}),t.length>1&&o!=="available"&&a.jsxs("div",{className:"space-y-1 mt-4 pt-3 border-t border-gray-700/30",children:[a.jsxs("div",{className:"flex justify-between text-xs",children:[a.jsx("span",{className:"text-gray-300",children:"Overall Progress"}),a.jsxs("span",{className:"text-gray-300",children:[a.jsx("span",{className:"font-accent",children:r.currentProgress}),"/",a.jsx("span",{className:"font-accent",children:r.totalRequired})]})]}),a.jsx("div",{className:"w-full h-2 bg-gray-800 rounded-full overflow-hidden",children:a.jsx("div",{style:{width:`${r.progressPercentage}%`},className:I("h-full rounded-full",o==="in_progress"?"bg-blue-600":"bg-green-600")})})]})]}),a.jsx(xn,{collapsible:!0,type:"single",defaultValue:"null",className:"bg-gray-800/80 rounded-lg border border-gray-700 mb-3 overflow-hidden",children:a.jsxs(mt,{value:"rewards",className:"border-0",children:[a.jsx(pt,{className:"p-3 hover:no-underline",children:a.jsxs("h5",{className:"text-sm text-white font-medium flex items-center gap-1",children:[a.jsx(It,{className:"size-4 text-yellow-400"}),a.jsx("span",{className:"font-display text-stroke-sm",children:"Rewards"})]})}),a.jsxs(ht,{className:"px-3 border-t border-gray-700/50 pt-2",children:[a.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.xpReward>0&&a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx("div",{className:"size-6 rounded-full bg-purple-900/30 border border-purple-700/50 flex items-center justify-center",children:a.jsx(Rt,{className:"size-3 text-purple-500"})}),a.jsxs("span",{className:"text-purple-300 text-xs",children:[a.jsx("span",{className:"font-accent",children:e.xpReward})," XP"]})]}),e.repReward>0&&a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx("div",{className:"size-6 rounded-full bg-red-900/30 border border-red-700/50 flex items-center justify-center",children:a.jsx(Ct,{className:"size-3 text-red-500"})}),a.jsxs("span",{className:"text-red-300 text-xs",children:[a.jsx("span",{className:"font-accent",children:e.repReward})," REP"]})]}),e.cashReward>0&&a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx("div",{className:"size-6 rounded-full bg-yellow-900/30 border border-yellow-700/50 flex items-center justify-center",children:a.jsx(kt,{className:"size-3 text-yellow-500"})}),a.jsxs("span",{className:"text-yellow-300 text-xs",children:[a.jsx("span",{className:"font-accent",children:e.cashReward})," Yen"]})]}),e.talentPointReward>0&&a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx("div",{className:"size-6 rounded-full bg-blue-900/30 border border-blue-700/50 flex items-center justify-center",children:a.jsx(Pe,{className:"size-3 text-blue-500"})}),a.jsxs("span",{className:"text-blue-300 text-xs",children:[a.jsx("span",{className:"font-accent",children:e.talentPointReward})," ",e.talentPointReward>1?"Points":"Point"]})]})]}),a.jsx("div",{className:"grid grid-cols-2 gap-2 mt-4",children:e.quest_reward.map((i,c)=>a.jsxs("div",{className:"flex items-center gap-2",children:[i.rewardType==="ITEM"&&a.jsx("div",{className:"size-10 rounded-full bg-blue-900/30 border border-blue-700/50 flex items-center justify-center",children:a.jsx(At,{item:i.item})}),a.jsx("div",{className:"text-sm",children:i.rewardType==="ITEM"&&a.jsx("div",{children:a.jsxs("span",{className:I("text-sm",i.item?.rarity==="novice"?"text-gray-300":i.item?.rarity==="standard"?"text-green-300":i.item?.rarity==="specialist"?"text-blue-300":"text-purple-300"),children:[a.jsx("span",{className:"font-accent",children:i.quantity}),"x"," ",i.item?.name]})})})]},c))})]})]})}),a.jsxs("div",{className:"flex justify-center",children:[o==="available"&&a.jsx("button",{className:"px-4 py-2 text-stroke-sm bg-yellow-600 hover:bg-yellow-700 text-white text-sm rounded-lg transition-colors text-stroke-s-sm! font-display",onClick:()=>s({id:e.id}),children:"Start Quest"}),o==="ready_to_complete"&&a.jsx("button",{className:"px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-lg transition-colors",onClick:()=>n({id:e.id}),children:"Complete Quest"}),o==="complete"&&a.jsx("p",{className:"text-gray-300 text-sm",children:"Completed"})]})]})}const $n=(e,t)=>{if(!e.isStoryQuest)return"";switch(t){case"available":return"border-amber-600/70 bg-gradient-to-r from-amber-900/20 to-amber-800/10";case"in_progress":return"border-amber-500/70 bg-gradient-to-r from-amber-900/30 to-amber-800/15";case"ready_to_complete":return"border-amber-400/80 bg-gradient-to-r from-amber-900/40 to-amber-800/20 shadow-amber-500/20 shadow-lg";case"complete":return"border-amber-700/50 bg-gradient-to-r from-amber-900/15 to-amber-800/5";default:return"border-amber-600/50"}},Pn=e=>{if(!e.isStoryQuest||!e.story_chapter)return null;const t=e.story_chapter.story_season,r=e.story_chapter;return t?`${t.name} - Chapter ${r.order}: ${r.name}`:`Chapter ${r.order}: ${r.name}`},Sn=(e,t)=>{if(!e.isStoryQuest)return"";switch(t){case"available":return"📖";case"in_progress":return"⭐";case"ready_to_complete":return"✨";case"complete":return"📚";default:return"📖"}},gt=(e,t=0,r)=>{if(!e.quest_progress||e.quest_progress.length===0)return 0;const s=e.quest_objective?.[t];if(!s)return 0;const n=ft(s,r);return n?Math.min(n,s.quantity||0):0},_n=(e,t)=>{if(!e.quest_objective||e.quest_objective.length===0)return{currentProgress:0,totalRequired:0,percentage:0};let r=0,s=0;return e.quest_objective.forEach((n,o)=>{s+=n.quantity||0,r+=gt(e,o,t)}),{currentProgress:r,totalRequired:s,percentage:s>0?r/s*100:0}},In=(e,t,r)=>{t.includes(e)?r(t.filter(s=>s!==e)):r([...t,e])};function Rn({quest:e,questGivers:t,availableQuests:r=[],inventory:s=[],expandedQuests:n,setExpandedQuests:o,isPinned:i=!1}){const c=G(e),d=n.includes(e.id),u=e.quest_objective?.length>1,p=e.quest_objective?.[0],{currentProgress:l,totalRequired:f,percentage:m}=_n(e,s),v=t?.find(x=>x.id===e.shopId)||t?.[0],j=Pn(e),N=Sn(e,c),$=$n(e,c);return a.jsxs("div",{className:I("bg-gray-800/50 rounded-lg border transition-colors",e.isStoryQuest&&$?$:i?"border-amber-600/50":c==="available"?"border-yellow-700/50":c==="in_progress"?"border-blue-700/50":c==="ready_to_complete"?"border-purple-700/50":"border-green-700/50"),children:[a.jsxs("div",{className:"p-2 flex items-center justify-between cursor-pointer",onClick:()=>In(e.id,n,o),children:[a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx("div",{className:"relative",children:a.jsx("div",{className:"size-8 rounded-full bg-linear-to-br from-gray-700 to-gray-900 flex items-center justify-center overflow-hidden border border-gray-600",children:a.jsx(gn,{src:v,width:32,height:32})})}),a.jsxs("div",{className:"flex-1",children:[a.jsxs("div",{className:"flex items-center gap-1.5",children:[e.isStoryQuest&&N&&a.jsx("span",{className:"text-sm",children:N}),a.jsx("h4",{className:I("text-sm font-medium font-display",e.isStoryQuest?"text-amber-300":"text-custom-yellow"),children:e.name}),e.isStoryQuest&&a.jsx(oe,{className:"size-3 text-amber-400"})]}),a.jsxs("div",{className:"flex items-center gap-1 text-xs",children:[e.isStoryQuest&&j?a.jsx("span",{className:"text-amber-400 font-medium",children:j}):a.jsx("span",{className:"text-blue-400",children:v?.name}),e.levelReq>1&&a.jsxs(a.Fragment,{children:[a.jsx("span",{className:"text-gray-500",children:"•"}),a.jsxs("span",{className:"text-gray-300",children:["Lvl ",e.levelReq,"+"]})]})]})]})]}),a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx("div",{className:"flex -space-x-1",children:r?.includes(e)&&a.jsx("div",{className:"size-5 rounded-full bg-yellow-900/30 border border-yellow-700/50 flex items-center justify-center",children:a.jsx(sr,{className:"size-3 text-yellow-500"})})}),d?a.jsx(Tt,{className:"size-5 text-gray-400"}):a.jsx(Ke,{className:"size-5 text-gray-400"})]})]}),c==="in_progress"&&!d&&a.jsxs("div",{className:"px-2 pb-2",children:[a.jsx("div",{className:"flex justify-between text-xs mb-1",children:u?a.jsxs(a.Fragment,{children:[a.jsx("span",{className:"text-gray-300",children:"Multiple Objectives"}),a.jsxs("span",{className:"text-gray-300",children:[l,"/",f]})]}):a.jsxs(a.Fragment,{children:[a.jsx("span",{className:"text-gray-300",children:p?Ie(p):"No objectives"}),a.jsxs("span",{className:"text-gray-300",children:[gt(e,0,s),"/",p?.quantity||0]})]})}),a.jsx("div",{className:"w-full h-1.5 bg-gray-800 rounded-full overflow-hidden",children:a.jsx("div",{className:"h-full bg-blue-600 rounded-full",style:{width:`${m}%`}})})]}),d&&a.jsx(Nn,{quest:e,objectives:e.quest_objective,overallProgress:{currentProgress:l,totalRequired:f,progressPercentage:m}})]},e.id)}function ve({quests:e,questGivers:t,availableQuests:r=[],inventory:s=[],expandedQuests:n,setExpandedQuests:o,showStatusGroups:i=!0,isPinned:c=!1,title:d,titleIcon:u}){if(e.length===0)return null;const p=l=>a.jsx(Rn,{quest:l,questGivers:t,availableQuests:r,inventory:s,expandedQuests:n,setExpandedQuests:o,isPinned:c},l.id);return i?a.jsx("div",{className:"space-y-2",children:["ready_to_complete","in_progress","available","complete"].map(l=>{const f=e.filter(m=>G(m)===l);return f.length===0?null:a.jsxs("div",{className:"mb-4",children:[a.jsxs("div",{className:"text-sm font-medium uppercase tracking-wider mt-6 mb-2 px-2 text-gray-400",children:[l==="ready_to_complete"&&"Ready to Complete",l==="in_progress"&&"In Progress",l==="available"&&"Available",l==="complete"&&"Completed"]}),a.jsx("div",{className:"space-y-2",children:f.map(p)})]},l)})}):a.jsxs("div",{className:"space-y-2",children:[d&&a.jsxs("div",{className:"text-sm font-medium uppercase tracking-wider px-2 text-amber-400 flex items-center gap-2",children:[u,d]}),a.jsx("div",{className:"space-y-2",children:e.map(p)})]})}function Cn({sortOption:e,setSortOption:t,sortDirection:r,setSortDirection:s,filterOptions:n,setFilterOptions:o,questGivers:i,showFilters:c,setShowFilters:d,activeTab:u}){const p=y.useMemo(()=>n.status&&n.status!=="all"||n.minLevel!==void 0||n.maxLevel!==void 0||n.questGiverId!==void 0||n.hasItemRewards===!0||n.hasTalentPointRewards===!0||n.location!==void 0&&n.location!=="any",[n]),l=()=>{o({status:"all"})},f=()=>{s(r==="asc"?"desc":"asc")};return a.jsxs("div",{className:"space-y-2 absolute right-2",children:[a.jsxs("div",{className:"flex items-center justify-end gap-4",children:[a.jsxs("div",{className:"cursor-pointer text-xs text-gray-400 text-stroke-0 flex items-center gap-1",onClick:()=>f(),children:[a.jsx("span",{className:"text-gray-300 font-bold uppercase font-mono",children:e.replace("_"," ")}),a.jsx("span",{className:"text-xs text-gray-300",children:r==="asc"?"↑":"↓"})]}),a.jsxs("button",{className:I("px-3 py-2 text-sm rounded-md flex items-center gap-1.5",p?"bg-indigo-900/50 text-indigo-300 border border-indigo-900/70":"bg-gray-800/60 text-gray-400 border border-gray-800"),onClick:()=>d(!c),children:[a.jsx(or,{className:"size-3"}),p&&a.jsx("span",{className:"size-2 rounded-full bg-indigo-400"})]})]}),c&&a.jsxs("div",{className:"bg-gray-800/70 border border-gray-700 rounded-lg p-3 space-y-3",children:[a.jsxs("div",{className:"space-y-1",children:[a.jsx("label",{className:"text-xs text-gray-400",children:"Sort By"}),a.jsxs("select",{value:e,className:"w-full bg-gray-900 border border-gray-700 rounded-md px-2 py-1 text-xs text-gray-300",onChange:m=>t(m.target.value),children:[a.jsx("option",{value:"level",children:"Level"}),a.jsx("option",{value:"name",children:"Name"}),a.jsx("option",{value:"Yen_reward",children:"Yen Reward"}),a.jsx("option",{value:"XP_reward",children:"XP Reward"}),a.jsx("option",{value:"REP_reward",children:"REP Reward"})]})]}),a.jsxs("div",{className:"border-t border-gray-700/50 pt-3",children:[a.jsx("h3",{className:"text-sm text-gray-300 font-medium mb-2",children:"Filters"}),a.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[a.jsxs("div",{className:"space-y-1",children:[a.jsx("label",{className:"text-xs text-gray-400",children:"Status"}),a.jsxs("select",{disabled:u==="complete",value:n.status||"all",className:"w-full bg-gray-900 border border-gray-700 rounded-md px-2 py-1 text-xs text-gray-300",onChange:m=>o({...n,status:m.target.value}),children:[a.jsx("option",{value:"all",children:u==="complete"?"Completed":"All Statuses"}),a.jsx("option",{value:"available",children:"New"}),a.jsx("option",{value:"in_progress",children:"In Progress"}),a.jsx("option",{value:"ready_to_complete",children:"Ready to turn in"})]})]}),a.jsxs("div",{className:"space-y-1",children:[a.jsx("label",{className:"text-xs text-gray-400",children:"Quest Giver"}),a.jsxs("select",{value:n.questGiverId||"",className:"w-full bg-gray-900 border border-gray-700 rounded-md px-2 py-1 text-xs text-gray-300",onChange:m=>o({...n,questGiverId:m.target.value?Number.parseInt(m.target.value):void 0}),children:[a.jsx("option",{value:"",children:"All Givers"}),i&&i.filter(m=>m.shopType!=="gang").map(m=>a.jsx("option",{value:m.id,children:m.name},m.id))]})]})]}),a.jsxs("div",{className:"space-y-1 mt-3",children:[a.jsx("label",{className:"text-xs text-gray-400",children:"Location"}),a.jsxs("select",{value:n.location||"any",className:"w-full bg-gray-900 border border-gray-700 rounded-md px-2 py-1 text-xs text-gray-300",onChange:m=>o({...n,location:m.target.value}),children:[a.jsx("option",{value:"any",children:"Any Location"}),a.jsx("option",{value:"church",children:"Church"}),a.jsx("option",{value:"shrine",children:"Shrine"}),a.jsx("option",{value:"mall",children:"Mall"}),a.jsx("option",{value:"alley",children:"Alley"}),a.jsx("option",{value:"school",children:"School"}),a.jsx("option",{value:"sewers",children:"Sewers"})]})]}),a.jsxs("div",{className:"space-y-1 mt-3",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("label",{className:"text-xs text-gray-400",children:"Level Range"}),(n.minLevel!==void 0||n.maxLevel!==void 0)&&a.jsx("button",{className:"text-xs text-indigo-400 hover:text-indigo-300",onClick:()=>o({...n,minLevel:void 0,maxLevel:void 0}),children:"Reset"})]}),a.jsxs("div",{className:"flex gap-2",children:[a.jsx("input",{type:"number",placeholder:"Min",value:n.minLevel||"",min:"1",className:"w-full bg-gray-900 border border-gray-700 rounded-md px-2 py-1 text-xs text-gray-300",onChange:m=>o({...n,minLevel:m.target.value?Number.parseInt(m.target.value):void 0})}),a.jsx("span",{className:"text-gray-400 self-center",children:"-"}),a.jsx("input",{type:"number",placeholder:"Max",value:n.maxLevel||"",min:"1",className:"w-full bg-gray-900 border border-gray-700 rounded-md px-2 py-1 text-xs text-gray-300",onChange:m=>o({...n,maxLevel:m.target.value?Number.parseInt(m.target.value):void 0})})]})]}),a.jsxs("div",{className:"flex gap-6 mt-3",children:[a.jsxs("label",{className:"flex items-center gap-1.5 cursor-pointer",children:[a.jsx("input",{type:"checkbox",checked:n.hasItemRewards===!0,className:"rounded text-indigo-500 focus:ring-indigo-500 border-gray-700 bg-gray-900",onChange:m=>o({...n,hasItemRewards:m.target.checked||void 0})}),a.jsx("span",{className:"text-xs text-gray-300",children:"Has Item Rewards"})]}),a.jsxs("label",{className:"flex items-center gap-1.5 cursor-pointer",children:[a.jsx("input",{type:"checkbox",checked:n.hasTalentPointRewards===!0,className:"rounded text-indigo-500 focus:ring-indigo-500 border-gray-700 bg-gray-900",onChange:m=>o({...n,hasTalentPointRewards:m.target.checked||void 0})}),a.jsx("span",{className:"text-xs text-gray-300",children:"Has Talent Point Rewards"})]})]})]}),a.jsxs("div",{className:"flex justify-end gap-2 pt-2 border-t border-gray-700/50 mt-2",children:[a.jsx("button",{className:"px-3 py-1 text-xs rounded-md bg-gray-900/70 text-gray-300 border border-gray-700 hover:bg-gray-900",onClick:()=>d(!1),children:"Close"}),p&&a.jsx("button",{className:"px-3 py-1 text-xs rounded-md bg-red-900/30 text-red-300 border border-red-900/50 hover:bg-red-900/50",onClick:l,children:"Clear Filters"})]})]})]})}function kn(){return a.jsx("div",{className:"bg-gradient-to-r from-amber-900/30 to-amber-800/20 border border-amber-700/50 rounded-lg p-3",children:a.jsxs("div",{className:"flex items-center gap-3",children:[a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx("div",{className:"size-8 rounded-full bg-amber-900/50 border border-amber-600/50 flex items-center justify-center",children:a.jsx(oe,{className:"size-4 text-amber-300"})}),a.jsxs("div",{children:[a.jsx("h3",{className:"text-amber-300 font-display font-medium text-sm",children:"Main Story"}),a.jsx("p",{className:"text-amber-400/80 text-xs",children:"Follow the academy's narrative through story quests"})]})]}),a.jsxs("div",{className:"ml-auto flex items-center gap-1 text-xs text-amber-400/70",children:[a.jsx(Ut,{className:"size-3"}),a.jsx("span",{children:"Auto-starting quests"})]})]})})}function Y({name:e,count:t,isActive:r,onClick:s,color:n}){return a.jsxs("button",{className:I("flex-1 font-display px-3 py-2 rounded-lg text-sm transition-colors flex items-center justify-center gap-1.5",r?n==="yellow"?"bg-yellow-900/30 text-yellow-300 border border-yellow-900/50":n==="blue"?"bg-blue-900/30 text-blue-300 border border-blue-900/50":"bg-green-900/30 text-green-300 border border-green-900/50":"bg-gray-800/30 text-gray-400 border border-gray-800 hover:text-gray-300"),onClick:s,children:[e,a.jsx("span",{className:I("px-1.5 py-0.5 rounded-full text-xs",r?n==="yellow"?"bg-yellow-900/50 text-yellow-300":n==="blue"?"bg-blue-900/50 text-blue-300":"bg-green-900/50 text-green-300":"bg-gray-800 text-gray-400"),children:t})]})}function An({activeTab:e,setActiveTab:t,currentQuestLength:r,completedQuestLength:s,storyQuestLength:n,setFilterOptions:o}){return a.jsxs("div",{className:"flex space-x-1",children:[a.jsx(Y,{name:"Main Story",count:n,isActive:e==="story",color:"blue",onClick:()=>{t("story"),o({status:"all"})}}),a.jsx(Y,{name:"Current",count:r,isActive:e==="current",color:"yellow",onClick:()=>{t("current"),o({status:"all"})}}),a.jsx(Y,{name:"Completed",count:s,isActive:e==="completed",color:"green",onClick:()=>{t("completed"),o({status:"all"})}})]})}const Un=[{id:1,name:"Nagao",shopType:"food",avatar:"https://d13cmcqz8qkryo.cloudfront.net/static/characters/Nagao/happyopen.webp",description:"Nagao's food shop",disabled:null},{id:2,name:"Goda",shopType:"weapon",avatar:"https://d13cmcqz8qkryo.cloudfront.net/static/characters/Goda/happyopen.webp",description:"Goda's weapon shop",disabled:null},{id:3,name:"Mihara",shopType:"armour",avatar:"https://d13cmcqz8qkryo.cloudfront.net/static/characters/Mihara/happy.webp",description:"Mihara's armor shop",disabled:null},{id:4,name:"Shoko",shopType:"general",avatar:"https://d13cmcqz8qkryo.cloudfront.net/static/characters/Shoko/happyopen.webp",description:"Shoko's general shop",disabled:null},{id:5,name:"Otake",shopType:"general",avatar:"https://d13cmcqz8qkryo.cloudfront.net/static/characters/Otake/neutral.webp",description:"???",disabled:null},{id:6,name:"Honda",shopType:"furniture",avatar:"https://d13cmcqz8qkryo.cloudfront.net/static/characters/Honda/neutral.webp",description:"???",disabled:!0}];function On(){const{data:e,isLoading:t}=W(_.shop.shopList.queryOptions({staleTime:Number.POSITIVE_INFINITY,placeholderData:Un})),{data:r,isLoading:s}=ir(),{data:n,isLoading:o}=cr(),{data:i,isLoading:c}=Et(),{data:d,isLoading:u}=lr(),{data:p}=Ce(),[l,f]=y.useState("current"),[m,v]=y.useState([]),[j,N]=y.useState(!1),x=[...r||[],...i||[]].filter(L=>!L.isStoryQuest),S=(n||[]).filter(L=>!L.isStoryQuest),C=l!=="story"?d||[]:[],U=l==="current"?x:l==="story"?d||[]:S,{filteredAndSortedQuests:k,sortOption:X,setSortOption:E,sortDirection:xt,setSortDirection:yt,filterOptions:bt,setFilterOptions:he}=vn(U),vt=t||s||o||c||u;return a.jsxs("div",{className:"space-y-3 p-2 max-w-(--breakpoint-md) mx-auto relative",children:[a.jsx(An,{activeTab:l,setActiveTab:f,currentQuestLength:x?.length||0,completedQuestLength:S?.length||0,storyQuestLength:d?.length||0,setFilterOptions:he}),l==="current"&&a.jsx(dr,{}),l==="story"&&a.jsx(kn,{}),a.jsxs(Lt,{isLoading:vt,size:16,children:[C.length>0&&a.jsx("div",{className:"mb-4",children:a.jsx(ve,{isPinned:!0,showStatusGroups:!1,questGivers:e||[],availableQuests:i||[],inventory:p||[],expandedQuests:m,setExpandedQuests:v,title:"Story Progress",titleIcon:a.jsx(oe,{className:"size-4"}),quests:C.filter(L=>G(L)!=="complete")})}),a.jsx(Cn,{sortOption:X,setSortOption:E,sortDirection:xt,setSortDirection:yt,filterOptions:bt,setFilterOptions:he,questGivers:e||[],showFilters:j,setShowFilters:N,activeTab:l}),k?.length===0?a.jsx("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 text-center",children:a.jsx("p",{className:"text-gray-400",children:"No tasks to show!"})}):a.jsx(ve,{showStatusGroups:!0,quests:k,questGivers:e||[],availableQuests:i,inventory:p,expandedQuests:m,setExpandedQuests:v})]})]})}export{On as default};
