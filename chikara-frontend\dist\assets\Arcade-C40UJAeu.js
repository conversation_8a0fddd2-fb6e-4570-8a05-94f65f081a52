import{r as s,j as e,g as f,aj as p,t as g,bQ as h,v as b,S as v}from"./index-DC0BXKDb.js";import{A as w}from"./arrow-left-DyjAfoBb.js";const j=({number:r=20})=>{const[a,o]=s.useState([]);return s.useEffect(()=>{const t=[...new Array(r)].map(()=>({top:-5,left:Math.floor(Math.random()*800-400)+"px",animationDelay:Math.random()*.6+.2+"s",animationDuration:Math.floor(Math.random()*8+2)+"s"}));o(t)},[r]),e.jsx(e.Fragment,{children:[...a].map((t,n)=>e.jsx("span",{style:t,className:f("absolute top-1/2 left-1/2 size-0.5 rotate-215 animate-meteor rounded-full bg-slate-500 shadow-[0_0_0_1px_#ffffff10]"),children:e.jsx("div",{style:{position:"absolute",top:"50%",transform:"translateY(-50%)",width:"50px",height:"1px",background:"linear-gradient(90deg, #64748b, transparent)"}})},n))})},k="/assets/terraformerlogo-ad7GCudx.png";function _(){const[r,a]=s.useState(!1),[o,t]=s.useState(!1),[n,l]=s.useState(!1),{setIframeActive:i}=p(),{data:d}=g();async function c(){}s.useEffect(()=>{const m=u=>{u.origin==="https://terraformer.vercel.app"&&u.data.message==="gameComplete"&&(a(!0),window.removeEventListener("message",m))};return window.addEventListener("message",m),()=>{window.removeEventListener("message",m)}},[]),s.useEffect(()=>{r&&(c(),h.capture("terraformer_completed",{}))},[r]);const x=()=>{d?.level>=15&&(i(!0),t(!0),l(!0))};return o?e.jsx(e.Fragment,{children:o&&e.jsx(y,{gameURL:"https://terraformer.vercel.app/?chikara=true",iframeLoading:n,setIframeActive:i,setGameOpen:t,setIframeLoading:l,title:"Terraformer"})}):e.jsxs("div",{className:"mt-4 flex size-full flex-col items-center gap-4 p-5 md:p-0",children:[e.jsxs("button",{type:"button",className:"group relative mx-auto flex h-24 w-full cursor-pointer select-none flex-col items-center justify-center overflow-hidden rounded-lg border border-gray-700 bg-background hover:ring-2 active:scale-[97%] md:h-32 md:w-3/5",onClick:()=>x(),children:[e.jsx(j,{number:20}),e.jsxs("div",{className:"flex flex-row-reverse gap-4 pr-6 md:flex-row md:gap-5 md:pl-20",children:[e.jsxs("div",{className:"my-auto",children:[" ",e.jsx("p",{className:"z-10 whitespace-pre-wrap text-center font-medium text-3xl text-black text-stroke-md tracking-tighter dark:text-custom-yellow",children:"TERRAFORMER"}),d?.level<15?e.jsx("p",{className:"z-10 font-medium text-red-500 text-xl transition-transform group-hover:scale-[1.2] group-hover:text-red-400 dark:text-stroke-s-sm",children:"Unlocked at level 15"}):e.jsxs("p",{className:"z-10 font-medium text-2xl text-blue-500 transition-transform group-hover:scale-[1.2] group-hover:text-sky-400 dark:text-stroke-s-sm",children:["Play Now ",e.jsx("span",{className:"text-gray-400 text-lg",children:"(Demo)"})]})]}),e.jsx("img",{className:"z-10 my-auto h-24 w-auto rounded-full group-hover:brightness-110 md:h-36",src:k,alt:""})]}),e.jsx("div",{className:"pointer-events-none absolute inset-0 h-full bg-[radial-gradient(circle_at_50%_120%,rgba(120,119,198,0.3),rgba(255,255,255,0))]"})]}),e.jsxs("button",{disabled:!0,type:"button",className:"relative mx-auto flex h-24 w-full select-none flex-col items-center justify-center overflow-hidden rounded-lg border border-gray-700 bg-background grayscale md:h-32 md:w-1/2",children:[e.jsx("p",{className:"z-10 whitespace-pre-wrap text-center font-medium text-4xl text-black tracking-tighter dark:text-gray-400 dark:text-stroke-s-sm",children:"COMING SOON"}),e.jsx("div",{className:"pointer-events-none absolute inset-0 h-full bg-[radial-gradient(circle_at_50%_120%,rgba(120,119,198,0.3),rgba(255,255,255,0))]"})]}),e.jsxs("button",{disabled:!0,type:"button",className:"relative mx-auto flex h-24 w-full select-none flex-col items-center justify-center overflow-hidden rounded-lg border border-gray-700 bg-background grayscale md:h-32 md:w-1/2",children:[e.jsx("p",{className:"z-10 whitespace-pre-wrap text-center font-medium text-4xl text-black tracking-tighter dark:text-gray-400 dark:text-stroke-s-sm",children:"COMING SOON"}),e.jsx("div",{className:"pointer-events-none absolute inset-0 h-full bg-[radial-gradient(circle_at_50%_120%,rgba(120,119,198,0.3),rgba(255,255,255,0))]"})]})]})}const y=({gameURL:r,iframeLoading:a,setIframeActive:o,setGameOpen:t,setIframeLoading:n,title:l=null})=>{const i=b(),d={border:"0px #ffffff none",height:"calc(100dvh - 3.75rem)",width:"100%"},c={border:"2px solid rgb(55 65 81)",borderRadius:"25px",height:"calc(100vh - 160px)",width:"100%"};return e.jsxs("div",{className:"relative md:mx-auto md:max-w-(--breakpoint-2xl)",children:[a&&e.jsx("div",{className:"mt-24 flex size-full",children:e.jsx("div",{className:"m-auto",children:e.jsx(v,{})})}),e.jsxs("button",{type:"button",className:f(a?"md:hidden":"md:inline-flex","mt-0 mb-2 hidden items-center rounded-md border border-transparent bg-indigo-700 px-6 py-2 text-md text-white shadow-xs hover:bg-indigo-800 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:inline-flex"),onClick:()=>{o(!1),t(!1)},children:[e.jsx(w,{className:"mr-3 size-5"}),"Back"]}),e.jsx("iframe",{allowFullScreen:!0,className:f(a&&"hidden","fixed top-16 left-0 md:relative md:top-0"),title:l,src:r,style:i?d:c,name:"gameIframe",onLoad:()=>n(!1)})]})};export{_ as default};
