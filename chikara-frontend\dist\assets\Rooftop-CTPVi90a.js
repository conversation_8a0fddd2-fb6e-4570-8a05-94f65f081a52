import{a as g,d as N,v as y,B as b,r as p,j as e,g as j,d3 as w,ae as C,C as R,an as c,ao as v,y as i,b as O,t as P,L as k}from"./index-DC0BXKDb.js";import{A as D}from"./ag-theme-quartz-41SzhtwZ.js";const T=t=>{const{value:a}=t;return a?e.jsxs("div",{className:"relative flex size-full p-5 md:p-3",children:[e.jsx(C,{item:a,className:"mx-auto",height:"h-full"}),t.data?.itemRewardQuantity>1&&e.jsxs("p",{className:"-translate-x-1/2 absolute bottom-4 left-1/2 rounded-lg bg-black/25 px-1 text-center font-bold text-custom-yellow text-sm leading-0 md:bottom-1.5",children:["x",t.data?.itemRewardQuantity]})]}):e.jsx("p",{className:"mt-6 text-bold text-gray-400 text-lg",children:"?"})},L=t=>{const{value:a}=t;return e.jsxs("div",{className:j(t.data?.defeated&&"grayscale",t.data?.disabled&&"opacity-25 grayscale","relative flex h-full flex-col items-center justify-center py-0.5 md:w-full md:flex-row md:items-start md:justify-normal md:gap-4 md:p-2"),children:[e.jsx(w,{src:t.data.image,className:"size-14 rounded-lg border border-blue-800 md:h-full md:w-auto"}),e.jsx("p",{className:"text-wrap! text-center! text-xs! 2xl:text-base! font-semibold text-blue-400 md:my-auto",children:a}),e.jsxs("p",{className:"text-custom-yellow text-xs md:hidden",children:[t.data.rank," Rank"]})]})},A=({npcList:t,currentUser:a})=>{const l=g(),m=N(),r=y(),{ROOFTOP_BATTLE_AP_COST:n}=b(),x=async s=>{if(a?.actionPoints<n)return c.error("Not enough AP");try{const d=await v.post(`${i.ROOFTOP.BEGIN}`,{battleOpponentId:parseInt(s)});return await m.invalidateQueries({queryKey:[i.USER.CURRENTUSERINFO]}),l("/fight"),d}catch(d){const o=d.message||"Unknown error occurred";console.error(o),c.error(o)}},u=s=>e.jsx("div",{className:"flex size-full items-center justify-center",children:e.jsx(R,{disabled:s.data?.defeated||s.data?.disabled,className:"text-xs! md:text-sm! w-[90%] 2xl:w-1/2",type:"danger",onClick:()=>x(s.data.id),children:s.data?.defeated?"Defeated":e.jsxs("div",{className:"flex flex-col",children:[e.jsx("p",{className:"md:text-sm!",children:"Attack"}),e.jsxs("p",{className:"text-xs! md:-mt-1 mb-1",children:[n," AP"]})]})})}),[f,E]=p.useState([{headerName:"NPC",field:"name",cellRenderer:L},{headerName:"Rank",field:"rank",hide:r,cellClass:"mt-4 text-xl font-semibold text-custom-yellow"},{headerName:"Level",field:"level",wrapHeaderText:!0,autoHeaderHeight:!0,cellClass:"mt-5 text-lg font-bold text-red-500 !md:px-0",maxWidth:r?67:null},{headerName:"Reward",field:"item",cellRenderer:T,sortable:!1},{headerName:"",field:"static",cellRenderer:u,sortable:!1}]),h={flex:1,sortable:!0,suppressMovable:!0,filter:!1,resizable:!1,cellClass:"px-0.5! md:px-2! 2xl:px-6!"};return e.jsx("div",{className:"ag-theme-quartz-dark",style:{width:"100%",overflow:"auto"},children:e.jsx(D,{suppressCellFocus:!0,suppressRowHoverHighlight:!0,rowData:t,columnDefs:f,defaultColDef:h,domLayout:"autoHeight",rowHeight:r?100:80})})};function B(){const{data:t,isLoading:a}=O({queryKey:i.ROOFTOP.NPCLIST}),{data:l}=P();return e.jsx("section",{className:" mx-auto rounded-lg py-6 md:max-w-7xl",children:e.jsx("div",{className:" mx-auto h-full px-0 md:px-6",children:e.jsx("div",{className:"mx-auto h-full text-center",children:e.jsx("div",{className:"mt-4 w-full md:mx-0",children:e.jsx(k,{isLoading:a,children:e.jsx(A,{npcList:t,currentUser:l})})})})})})}export{B as default};
