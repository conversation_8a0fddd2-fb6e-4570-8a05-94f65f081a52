import{N as c,b as x,o as k,t as w,v,a9 as I,j as a,S as N,n as S,g as m,aa as i}from"./index-DC0BXKDb.js";import{T as y}from"./TraderRep-DWjXZ70I.js";const j="/assets/backgroundImg-cxMMHQS6.webp",G="/assets/happyopen-CRvy9v7Z.webp",B="/assets/backgroundImg-CaBgDC45.webp",C="/assets/neutral-DydpajS9.webp",M="/assets/neutralShadow-CTW1ix-z.webp",H="/assets/backgroundImg-CxrQswXt.webp",O="/assets/happy-D53xDMqa.webp",F="/assets/backgroundImg-C8THYgdB.webp",A="/assets/happyopen-BywJr-Fd.webp",L="/assets/backgroundImg-BZ7IgI6U.webp",T="/assets/neutral-CKcwP3Gg.webp",K="/assets/neutralShadow-DV9lvAE8.webp",R="/assets/backgroundImg-3EK4Ifxm.webp",D="/assets/happyopen-BjJ5ngIm.webp",E="/assets/happyopenShadow-mtmPWIpd.webp",P=r=>{switch(c(r)){case"Nagao":return A;case"Goda":return G;case"Mihara":return O;case"Shoko":return D;case"Otake":return T;case"Honda":return C;default:return""}},W=r=>{switch(c(r)){case"Shoko":return E;case"Otake":return K;case"Honda":return M;default:return""}},z=r=>{switch(c(r)){case"Nagao":return F;case"Goda":return j;case"Mihara":return H;case"Shoko":return R;case"Otake":return L;case"Honda":return B;default:return""}},U=r=>{switch(c(r)){case"Nagao":return"center";case"Goda":return"middle";case"Mihara":return"left";case"Shoko":return"center";case"Otake":return"left";case"Honda":return"center";default:return""}},Q=r=>{const s=z(r),d=U(r);return{backgroundImage:`url(${s})`,backgroundPosition:d,backgroundRepeat:"no-repeat",backgroundSize:"cover"}};function J(){const{isLoading:r,error:s,data:d}=x(k.shop.shopList.queryOptions()),{data:o}=w(),u=v(),g=I();if(s)return"An error has occurred: "+s.message;const l=(e,t)=>{const p={Nagao:{top:"12rem",left:"-0.5rem",color:"bg-linear-to-r from-purple-500 to-pink-500"},Goda:{top:"13rem",left:"2.5rem",color:"bg-linear-to-r from-cyan-500 to-blue-500"},Mihara:{top:"13rem",left:"1rem",color:"bg-linear-to-r from-yellow-100 via-yellow-300 to-yellow-500"},Shoko:{top:"12rem",left:"1.2rem",color:"bg-linear-to-r from-red-500 to-red-800"},Otake:{top:"13rem",left:"0.5rem",color:"bg-linear-to-r from-rose-400 to-orange-300"},Honda:{top:"13rem",left:"0.5rem",color:"bg-linear-to-r from-rose-400 to-orange-300"}},b={Nagao:{top:"6rem",left:"-0.5rem"},Goda:{top:"6.3rem",left:"1.25rem"},Mihara:{top:"6rem",left:"0.8rem"},Shoko:{top:"6rem",left:"0.6rem"},Otake:{top:"6.25rem",left:"0.5rem"},Honda:{top:"6rem",left:"0.5rem"}};return u&&(t==="left"||t==="top")?b[e][t]:p[e][t]},n=(e,t,p)=>!!(e.disabled||i()),h=(e,t)=>{if(t==="Otake")return"Rare Goods";switch(e){case"general":return"General Goods";case"weapon":return"Weapons";case"armour":return"Armor";case"food":return"Food";case"furniture":return"Sunday Shop";default:return""}},f=d?.filter(e=>e.shopType!=="gang");return a.jsx(a.Fragment,{children:r?a.jsx(N,{center:!0}):a.jsx("ul",{role:"list",className:"m-2 mb-12 grid h-[calc(100dvh)] grid-cols-2 gap-x-3 gap-y-4 sm:grid-cols-2 md:mx-auto md:mb-0 md:h-auto md:max-w-7xl md:grid-cols-2 md:gap-5 md:gap-y-20 lg:grid-cols-3 2xl:gap-y-4",children:f.map((e,t)=>a.jsx("li",{className:"col-span-1 flex max-h-[45vh] flex-col text-center",children:a.jsx("div",{className:"flex h-full flex-col p-0 md:flex-1",children:a.jsx(S,{className:"h-full",to:"/shops/"+e.id,style:{pointerEvents:n(e,o?.level)?"none":""},children:a.jsxs("figure",{style:Q(e.name),className:m(l(e.name,"color"),"shopKeeperAvatarWrapper relative inline-block size-full overflow-hidden rounded-md border-x-green-600 ring-3 ring-gray-300 drop-shadow-md hover:ring-[#7060FE] md:h-[26.6rem] dark:ring-gray-600"),children:[n(e,o?.level)?null:a.jsx("div",{className:"-translate-x-1/2 traderRepContainer absolute top-2 left-1/2 z-10 flex w-fit rounded-full border-2 border-slate-600/50 bg-slate-200/50 px-2 shadow-xl blur-[0.3px]",children:a.jsx(y,{heartWidth:"md:w-12 w-5",shopId:e.id})}),a.jsxs("figcaption",{className:`${n(e,o?.level)?"disabledShopKeeperName pb-3 text-[2rem] md:h-[130px] md:pb-0 md:text-[3rem]":"shopKeeperName pb-3 text-[2rem] md:h-[130px] md:pb-0 md:text-[3rem]"}`,children:[a.jsx("p",{className:"mt-8 drop-shadow-lg md:h-16",children:n(e,o?.level)?"???":e.name}),a.jsx("p",{className:"text-[#ffdc11] text-sm drop-shadow-2xl",children:n(e,o?.level)?"Open on Sundays":i("shop",t,o?.level)?`Unlocked at level ${i("shop",t,o?.level)}`:h(e.shopType,e.name)})]}),a.jsx("div",{className:m(g?"backdrop-blur-firefox":"backdrop-blur-[1px]","absolute"),children:a.jsx("img",{loading:"eager",alt:"Shopkeeper Avatar",className:m(l(e.name,"left"),n(e,o?.level)?"disabledShop":"shopKeeperAvatar"),src:n(e,o?.level)?W(e.name):P(e.name),style:{marginLeft:l(e.name,"left"),marginTop:l(e.name,"top")}})})]})})})},e.id))})})}export{J as default};
