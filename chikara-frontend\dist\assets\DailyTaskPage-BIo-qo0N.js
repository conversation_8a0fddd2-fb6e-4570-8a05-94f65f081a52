import{d as x,e as g,o as m,an as c,y as h,j as e,ae as f,a6 as y,cV as b,cW as j,t as w,cX as N,S as v,ah as k,cS as C,g as o,C as R,cT as D}from"./index-DC0BXKDb.js";import{g as Q}from"./greenTick-CRl6ZXFn.js";import{u as z,g as S,o as T}from"./useGetQuestObjectiveText-DejZoSM0.js";const E="/assets/dailychest-OOUVtqsz.png",I="/assets/lockedchest-Db25p4X2.png",U=()=>{const s=x();return g(m.dailyQuest.claimDailyCompletionReward.mutationOptions({onSuccess:t=>{c.success("You received 1x Daily Chest!"),s.invalidateQueries({queryKey:[h.USER.CURRENTUSERINFO]})},onError:t=>{c.error(`Error: ${t.message}`)}}))},$=()=>{const s=x();return g(m.dailyQuest.completeDailyQuest.mutationOptions({onSuccess:()=>{c.success("Task completed"),s.invalidateQueries({queryKey:m.dailyQuest.getDailyQuests.key()})},onError:t=>{const l=t.message||"Unknown error occurred";console.error(l),c.error(l)}}))},O="/assets/expicon-BeF7u3m_.png",B=({quest:s})=>{if(s.itemRewardId&&s.item)return e.jsx("div",{className:"h-12",children:e.jsx(f,{item:s.item,quantity:s.itemRewardQuantity})});let t="",l="",r="",i=s.quantity;return s?.cashReward>0&&(t=y,l="Yen",r=b,i=s?.cashReward),s?.xpReward>0&&(t=O,l="EXP",r=j,i=s?.xpReward),e.jsxs("div",{"data-tooltip-id":"questreward-tooltip","data-tooltip-content":l,className:"relative w-auto",children:[e.jsx("img",{src:r,className:"h-full w-auto bg-cover",alt:""}),e.jsx("img",{className:"-translate-x-1/2 -translate-y-1/2 absolute top-[47%] left-1/2 z-5 h-12 ",src:t,alt:`${l}`}),i&&e.jsx("p",{className:"absolute right-2 bottom-2 z-10 text-stroke-s-sm text-xs text-gray-200",children:i})]})},V=C(),_=s=>{switch(s){case"complete":return"bg-green-500";case"ready_to_complete":return"bg-yellow-300";case"in_progress":return"bg-zinc-300";default:return"bg-[#15151d]"}},X=()=>{const{data:s,isLoading:t}=z({select:a=>a.slice(0,3)}),{data:l}=w(),r=s?.filter(a=>a.questStatus==="complete"),i=$(),p=U(),n=l?.dailyQuestsRewardClaimed===N,d=r?.length>=s?.length;return t?e.jsx(v,{center:!0}):e.jsxs("div",{className:"relative mx-auto max-w-2xl md:h-full md:rounded-lg md:bg-gray-700",children:[e.jsxs("div",{className:"relative mb-2 flex h-12 justify-between border-black border-y-2 bg-[#343549] p-1 px-4 pb-1.5 md:rounded-t-lg -m-2",children:[e.jsx("h1",{className:"my-auto font-body font-semibold text-lg text-stroke-s-sm text-white",children:"Daily Tasks"}),e.jsxs("div",{className:"relative my-auto h-7 w-auto",children:[e.jsx("img",{className:"h-full w-auto",src:"https://cloudflare-image.jamessut.workers.dev/ui-images/AdXczms.png",alt:""}),e.jsxs("p",{"data-testid":"countdown-timer",className:"absolute top-[0.3rem] right-[2.2rem] text-sm text-white font-display",children:[" ",e.jsx(k,{showHours:!0,targetDate:V,showSeconds:!1})]})]}),e.jsx("div",{className:"absolute bottom-0 left-0 h-1 w-full bg-[#272839]"})]}),e.jsxs("div",{className:"mx-3 md:p-4 md:pb-12",children:[e.jsxs("div",{className:"mb-4 flex h-24 w-full gap-4 rounded-lg border-2 border-[#00ccff] bg-[#0099ff] p-2 ring-2 ring-black",children:[e.jsxs("div",{className:"my-auto flex w-4/6 flex-col gap-1",children:[e.jsx("p",{className:"font-body font-semibold text-base text-stroke-s-sm text-white",children:"Complete all of the daily tasks"}),e.jsx(u,{minValue:r?.length,maxValue:s?.length})]}),e.jsxs("div",{className:o("relative my-auto mr-5 ml-auto size-[4.7rem] md:mr-12"),children:[d&&n&&e.jsx("img",{className:"-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 z-30",src:Q,alt:""}),d?e.jsx("img",{src:E,alt:"",className:o(n?"opacity-50":"","absolute z-20 h-full w-auto cursor-pointer"),onClick:()=>p.mutate({})}):e.jsx("img",{className:"absolute z-20 h-full w-auto",src:I,alt:""}),e.jsx("img",{className:"absolute z-10 size-full scale-150",src:"https://cloudflare-image.jamessut.workers.dev/ui-images/kwFhD5T.png",alt:""}),d&&!n&&e.jsx("img",{className:"absolute z-10 size-full scale-150",src:"https://cloudflare-image.jamessut.workers.dev/ui-images/5I89C8Q.png",alt:""})]})]}),e.jsx("div",{className:"flex flex-col gap-4",children:s?.map(a=>e.jsx("div",{className:o("h-24 w-full gap-4 rounded-lg border-2 border-black",_(a.questStatus)),children:e.jsxs("div",{className:"flex size-full flex-row gap-4 p-3",children:[e.jsx("div",{className:"w-20",children:e.jsx(B,{quest:a})}),e.jsxs("div",{className:"flex w-full gap-4",children:[e.jsxs("div",{className:"flex w-full flex-col gap-1",children:[e.jsx("div",{className:o(T[a.objectiveType].length>30?"text-sm":"text-base","font-bold text-slate-900! text-stroke-0"),children:S(a)}),e.jsx("div",{className:"w-full",children:e.jsx(u,{minValue:Math.min(a.count,a.quantity),maxValue:a.quantity,className:"w-full! font-display"})})]}),e.jsx("div",{className:"my-auto ml-auto w-auto ",children:a.questStatus==="complete"?e.jsx("div",{className:"h-full w-20",children:e.jsx("img",{src:"https://cloudflare-image.jamessut.workers.dev/ui-images/M3NQxxd.png",alt:""})}):e.jsx("div",{className:"h-full w-20",children:e.jsx(R,{disabled:a.questStatus!=="ready_to_complete",className:"w-full font-display",onClick:()=>i.mutate({id:a.id}),children:"Claim"})})})]})]})},a.id))})]})]})},u=({minValue:s,maxValue:t,className:l})=>{const r=(s/t*100).toFixed(0);return e.jsx("div",{className:o("flex w-5/6 flex-row justify-center gap-4",l),children:e.jsx(D,{barClassName:"bg-[#00d200]",className:"bg-[#292939]! my-auto h-6 rounded-[5px] border-2 border-black font-display",value:r,displayText:`${s}/${t}`,backGradient:"bg-[#15151d]",frontGradient:"bg-[#0ff907]"})})};export{X as default};
