# useFetchCurrentUser Performance Benchmark

This directory contains **real performance benchmarks** to compare the performance of two API hook implementations:

- **New Hook**: `useFetchCurrentUser.tsx` (ORPC-based)
- **Old Hook**: `useFetchCurrentUserOld.tsx` (Axios-based)

## 🎯 What's Being Tested

The benchmarks measure key performance metrics using **actual React hooks**:

### Performance Metrics

- **Execution Time**: How long each hook takes to complete
- **Render Time**: Time taken for initial hook rendering
- **Query Time**: Time taken for the actual API call
- **Success Rate**: Percentage of successful API calls
- **Consistency**: Standard deviation of execution times

### Key Differences Being Measured

1. **ORPC vs Axios**: Modern RPC vs traditional REST API calls
2. **Query Options**: Built-in optimizations vs manual configuration
3. **Side Effects**: useEffect-based vs inline side effects
4. **Type Safety**: Full type safety vs manual typing
5. **Error Handling**: Built-in vs manual error handling

## 🚀 Running the Real Benchmarks

### 1. Real Hook Benchmark (Vitest)

```bash
# Run the real hook benchmark with mocked dependencies
npm run benchmark:hooks

# Run with watch mode for development
npm run test:watch src/benchmarks/realHookBenchmark.test.tsx
```

### 2. React Component Benchmark

Import the component into your app for live testing:

```tsx
import UseFetchCurrentUserBenchmark from "./benchmarks/useFetchCurrentUserBenchmark";

// Render in your app to test with real network conditions
<UseFetchCurrentUserBenchmark />;
```

## 📊 Benchmark Types

### 1. Real Hook Benchmark (`realHookBenchmark.test.tsx`) ✅ REAL

- **Purpose**: Tests actual hook implementations with mocked dependencies
- **Technology**: Vitest + React Testing Library
- **Pros**: Real React hook behavior, accurate render cycles, actual TanStack Query
- **Best for**: Accurate performance measurement of hook logic

### 2. React Component Benchmark (`useFetchCurrentUserBenchmark.tsx`) ✅ REAL

- **Purpose**: Full React component integration testing
- **Technology**: React + TanStack Query + Real Network
- **Pros**: Complete integration testing, real network conditions
- **Best for**: End-to-end performance validation in your actual app

## 🔧 Configuration Options

### Vitest Benchmark Configuration

You can modify the benchmark parameters in `realHookBenchmark.test.tsx`:

```typescript
// Adjust these values for different test scenarios
const benchmark = new RealHookBenchmark(
    50, // iterations - number of test runs per hook
    5 // warmupRuns - number of warmup iterations
);
```

### React Component Benchmark Options

The React component benchmark supports these configurations:

- **Iterations**: Number of test runs per hook
- **Warmup Runs**: Number of warmup iterations
- **Enable Cache**: Test with caching enabled/disabled
- **Network Delay**: Real network conditions (no simulation needed)

## 📈 Understanding Results

### Performance Improvements

- **Positive %**: New hook (ORPC) is better
- **Negative %**: Old hook (Axios) is better

### Key Metrics to Watch

1. **Total Execution Time**: Overall performance
2. **Query Time**: Network request efficiency
3. **Memory Usage**: Resource consumption
4. **Success Rate**: Reliability
5. **Standard Deviation**: Consistency

### Expected Results

Based on the implementation differences, you should expect:

**ORPC Hook Advantages:**

- ✅ Faster query setup (built-in optimizations)
- ✅ Better type safety (no runtime overhead)
- ✅ More consistent performance
- ✅ Better error handling

**Axios Hook Characteristics:**

- ⚠️ More manual configuration overhead
- ⚠️ Side effects in query function (potential performance impact)
- ⚠️ Manual error handling and parsing

## 🛠️ Customizing Benchmarks

### Adding New Metrics

To add custom performance metrics, modify the `PerformanceMetrics` interface:

```typescript
interface PerformanceMetrics {
    // Existing metrics...
    customMetric: number;
}
```

### Testing Different Scenarios

You can create scenario-specific benchmarks:

```typescript
// Test with different network conditions
const slowNetworkConfig = { networkDelay: 500 };
const fastNetworkConfig = { networkDelay: 10 };

// Test with different data sizes
const largeDataConfig = { dataSize: "large" };
const smallDataConfig = { dataSize: "small" };
```

### Mock Customization

Modify the mocks in `realHookBenchmark.test.tsx` to test different scenarios:

```typescript
// Simulate slow network
jest.mock("@/lib/orpc", () => ({
    orpc: {
        user: {
            getCurrentUserInfo: {
                queryOptions: jest.fn((options) => ({
                    queryFn: async () => {
                        await new Promise((resolve) => setTimeout(resolve, 200)); // Slow
                        return mockUserData;
                    },
                    ...options,
                })),
            },
        },
    },
}));
```

## 🎯 Best Practices

### Running Benchmarks

1. **Close other applications** to reduce system noise
2. **Run multiple times** and average the results
3. **Use consistent test data** across runs
4. **Test in production-like conditions** when possible

### Interpreting Results

1. **Look for trends** across multiple runs
2. **Consider real-world usage patterns**
3. **Factor in development experience** (type safety, debugging)
4. **Consider maintenance overhead**

### CI/CD Integration

Add to your CI pipeline:

```yaml
- name: Run Performance Benchmarks
  run: |
      npm run benchmark:hooks -- --iterations=50
      npm run benchmark:hooks:real
```

## 🔍 Troubleshooting

### Common Issues

1. **High variance in results**: Increase warmup runs or iterations
2. **Benchmark timeouts**: Reduce iterations or increase timeout
3. **Memory issues**: Run benchmarks separately or reduce iterations
4. **Inconsistent results**: Ensure system is idle during testing

### Debug Mode

Enable debug logging by setting environment variables:

```bash
DEBUG=benchmark npm run benchmark:hooks
```

## 📝 Contributing

To add new benchmarks or improve existing ones:

1. Follow the existing patterns for consistency
2. Add appropriate documentation
3. Include both positive and negative test cases
4. Test across different environments
5. Update this README with new features

## 🔗 Related Files

- `useFetchCurrentUser.tsx` - New ORPC-based implementation
- `useFetchCurrentUserOld.tsx` - Old Axios-based implementation
- `@/lib/orpc.ts` - ORPC client configuration
- `@/helpers/axiosInstance.ts` - Axios configuration
- `package.json` - Benchmark scripts configuration
