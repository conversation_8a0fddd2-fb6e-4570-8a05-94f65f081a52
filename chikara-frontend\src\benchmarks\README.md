# useFetchCurrentUser Performance Benchmark

This directory contains comprehensive benchmarks to compare the performance of two API hook implementations:

- **New Hook**: `useFetchCurrentUser.tsx` (ORPC-based)
- **Old Hook**: `useFetchCurrentUserOld.tsx` (Axios-based)

## 🎯 What's Being Tested

The benchmarks measure several key performance metrics:

### Performance Metrics
- **Execution Time**: How long each hook takes to complete
- **Render Time**: Time taken for initial hook rendering
- **Query Time**: Time taken for the actual API call
- **Memory Usage**: Memory consumption during execution
- **Success Rate**: Percentage of successful API calls
- **Consistency**: Standard deviation of execution times
- **Cache Performance**: Cache hit rates and effectiveness

### Key Differences Being Measured
1. **ORPC vs Axios**: Modern RPC vs traditional REST API calls
2. **Query Options**: Built-in optimizations vs manual configuration
3. **Side Effects**: useEffect-based vs inline side effects
4. **Type Safety**: Full type safety vs manual typing
5. **Error Handling**: Built-in vs manual error handling

## 🚀 Running the Benchmarks

### 1. Command Line Benchmark (Simulated)
```bash
# Run the simulated benchmark
npm run benchmark:hooks

# With custom parameters
npm run benchmark:hooks -- --iterations=200 --warmup=20

# Show help
npm run benchmark:hooks -- --help
```

### 2. Real Hook Benchmark (Jest)
```bash
# Run the real hook benchmark with mocked dependencies
npm run benchmark:hooks:real

# Run with watch mode
npm run test:watch src/benchmarks/realHookBenchmark.test.tsx
```

### 3. Visual Benchmark (Browser)
```bash
# Start the dev server
npm run dev

# Then open in browser:
# http://localhost:5173/src/benchmarks/index.html
```

Or directly open the HTML file in your browser:
```bash
# Open the standalone HTML file
open src/benchmarks/index.html
```

## 📊 Benchmark Types

### 1. Simulated Benchmark (`runBenchmark.ts`)
- **Purpose**: Quick performance comparison with simulated network conditions
- **Pros**: Fast execution, consistent results, configurable parameters
- **Cons**: Not testing real implementations
- **Best for**: Initial performance estimates and CI/CD integration

### 2. Real Hook Benchmark (`realHookBenchmark.test.tsx`)
- **Purpose**: Tests actual hook implementations with mocked dependencies
- **Pros**: Real React hook behavior, accurate render cycles
- **Cons**: Requires test environment setup
- **Best for**: Accurate performance measurement of hook logic

### 3. Visual Benchmark (`index.html`)
- **Purpose**: Interactive browser-based benchmark with visual results
- **Pros**: Easy to use, visual feedback, configurable parameters
- **Cons**: Simulated data, not real hooks
- **Best for**: Demonstrations and quick comparisons

### 4. React Component Benchmark (`useFetchCurrentUserBenchmark.tsx`)
- **Purpose**: Full React component integration testing
- **Pros**: Complete integration testing, real React Query behavior
- **Cons**: Complex setup, requires React environment
- **Best for**: End-to-end performance validation

## 🔧 Configuration Options

### Command Line Options
```bash
--iterations=N     # Number of benchmark iterations (default: 100)
--warmup=N         # Number of warmup runs (default: 10)
--no-mock          # Disable mocking (use real implementations)
--help             # Show help message
```

### Visual Benchmark Options
- **Iterations**: Number of test runs per hook
- **Warmup Runs**: Number of warmup iterations
- **Network Delay**: Simulated network latency (ms)
- **Enable Cache**: Test with caching enabled/disabled
- **Simulate Errors**: Include error scenarios in testing

## 📈 Understanding Results

### Performance Improvements
- **Positive %**: New hook (ORPC) is better
- **Negative %**: Old hook (Axios) is better

### Key Metrics to Watch
1. **Total Execution Time**: Overall performance
2. **Query Time**: Network request efficiency
3. **Memory Usage**: Resource consumption
4. **Success Rate**: Reliability
5. **Standard Deviation**: Consistency

### Expected Results
Based on the implementation differences, you should expect:

**ORPC Hook Advantages:**
- ✅ Faster query setup (built-in optimizations)
- ✅ Better type safety (no runtime overhead)
- ✅ More consistent performance
- ✅ Better error handling

**Axios Hook Characteristics:**
- ⚠️ More manual configuration overhead
- ⚠️ Side effects in query function (potential performance impact)
- ⚠️ Manual error handling and parsing

## 🛠️ Customizing Benchmarks

### Adding New Metrics
To add custom performance metrics, modify the `PerformanceMetrics` interface:

```typescript
interface PerformanceMetrics {
  // Existing metrics...
  customMetric: number;
}
```

### Testing Different Scenarios
You can create scenario-specific benchmarks:

```typescript
// Test with different network conditions
const slowNetworkConfig = { networkDelay: 500 };
const fastNetworkConfig = { networkDelay: 10 };

// Test with different data sizes
const largeDataConfig = { dataSize: 'large' };
const smallDataConfig = { dataSize: 'small' };
```

### Mock Customization
Modify the mocks in `realHookBenchmark.test.tsx` to test different scenarios:

```typescript
// Simulate slow network
jest.mock('@/lib/orpc', () => ({
  orpc: {
    user: {
      getCurrentUserInfo: {
        queryOptions: jest.fn((options) => ({
          queryFn: async () => {
            await new Promise(resolve => setTimeout(resolve, 200)); // Slow
            return mockUserData;
          },
          ...options,
        })),
      },
    },
  },
}));
```

## 🎯 Best Practices

### Running Benchmarks
1. **Close other applications** to reduce system noise
2. **Run multiple times** and average the results
3. **Use consistent test data** across runs
4. **Test in production-like conditions** when possible

### Interpreting Results
1. **Look for trends** across multiple runs
2. **Consider real-world usage patterns**
3. **Factor in development experience** (type safety, debugging)
4. **Consider maintenance overhead**

### CI/CD Integration
Add to your CI pipeline:

```yaml
- name: Run Performance Benchmarks
  run: |
    npm run benchmark:hooks -- --iterations=50
    npm run benchmark:hooks:real
```

## 🔍 Troubleshooting

### Common Issues
1. **High variance in results**: Increase warmup runs or iterations
2. **Benchmark timeouts**: Reduce iterations or increase timeout
3. **Memory issues**: Run benchmarks separately or reduce iterations
4. **Inconsistent results**: Ensure system is idle during testing

### Debug Mode
Enable debug logging by setting environment variables:

```bash
DEBUG=benchmark npm run benchmark:hooks
```

## 📝 Contributing

To add new benchmarks or improve existing ones:

1. Follow the existing patterns for consistency
2. Add appropriate documentation
3. Include both positive and negative test cases
4. Test across different environments
5. Update this README with new features

## 🔗 Related Files

- `useFetchCurrentUser.tsx` - New ORPC-based implementation
- `useFetchCurrentUserOld.tsx` - Old Axios-based implementation
- `@/lib/orpc.ts` - ORPC client configuration
- `@/helpers/axiosInstance.ts` - Axios configuration
- `package.json` - Benchmark scripts configuration
