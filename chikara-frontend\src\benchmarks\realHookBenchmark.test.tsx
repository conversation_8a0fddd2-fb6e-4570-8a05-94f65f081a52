/**
 * Real hook benchmark test using Vitest and React Testing Library
 * This tests the actual hook implementations with mocked dependencies
 */

import { renderHook, waitFor } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import React from "react";
import { vi, describe, test, beforeEach, afterEach, expect } from "vitest";
import useFetchCurrentUser from "../hooks/api/useFetchCurrentUser";
import useFetchCurrentUserOld from "../hooks/api/useFetchCurrentUserOld";

// Mock the dependencies
vi.mock("../src/lib/orpc", () => ({
    orpc: {
        user: {
            getCurrentUserInfo: {
                queryOptions: vi.fn((options) => ({
                    queryKey: ["getCurrentUserInfo"],
                    queryFn: async () => {
                        // Simulate network delay
                        await new Promise((resolve) => setTimeout(resolve, 50));
                        return mockUserData;
                    },
                    ...options,
                })),
            },
        },
    },
    QueryOptions: {},
}));

vi.mock("../src/helpers/axiosInstance", () => ({
    handleGet: vi.fn(async () => {
        // Simulate network delay
        await new Promise((resolve) => setTimeout(resolve, 75));
        return mockUserData;
    }),
}));

vi.mock("../src/helpers/apiRoutes", () => ({
    APIROUTES: {
        USER: {
            CURRENTUSERINFO: "getCurrentUserInfo",
        },
    },
}));

vi.mock("../../app/store/stores", () => ({
    usePersistStore: () => ({
        staleCurrentUserData: null,
        setStaleCurrentUserData: vi.fn(),
        gameConfig: { version: "1.0.0" },
    }),
    useAuthStore: () => true, // authed = true
}));

vi.mock("../../app/fetchGameConfig", () => ({
    fetchGameConfig: vi.fn(),
}));

// Mock user data
const mockUserData = {
    id: 1,
    username: "testuser",
    email: "<EMAIL>",
    gameConfigVersion: "1.0.0",
    level: 10,
    experience: 1000,
};

// Performance measurement utilities
interface BenchmarkResult {
    hookName: string;
    avgRenderTime: number;
    avgQueryTime: number;
    avgTotalTime: number;
    minTime: number;
    maxTime: number;
    standardDeviation: number;
    successRate: number;
}

class RealHookBenchmark {
    private iterations: number;
    private warmupRuns: number;

    constructor(iterations = 50, warmupRuns = 5) {
        this.iterations = iterations;
        this.warmupRuns = warmupRuns;
    }

    private createWrapper() {
        const queryClient = new QueryClient({
            defaultOptions: {
                queries: {
                    retry: false,
                    staleTime: 0,
                    gcTime: 0,
                },
            },
        });

        return ({ children }: { children: React.ReactNode }) => (
            <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
        );
    }

    async benchmarkHook(
        hookFn: typeof useFetchCurrentUser | typeof useFetchCurrentUserOld,
        hookName: string
    ): Promise<BenchmarkResult> {
        const times: number[] = [];
        const renderTimes: number[] = [];
        const queryTimes: number[] = [];
        let successCount = 0;

        // Warmup runs
        for (let i = 0; i < this.warmupRuns; i++) {
            const { unmount } = renderHook(() => hookFn(), {
                wrapper: this.createWrapper(),
            });
            unmount();
        }

        // Actual benchmark runs
        for (let i = 0; i < this.iterations; i++) {
            const startTime = performance.now();

            const { result, unmount } = renderHook(() => hookFn(), {
                wrapper: this.createWrapper(),
            });

            const renderEndTime = performance.now();
            const renderTime = renderEndTime - startTime;
            renderTimes.push(renderTime);

            // Wait for query to complete
            const queryStartTime = performance.now();
            try {
                await waitFor(
                    () => {
                        expect(result.current.isSuccess || result.current.isError).toBe(true);
                    },
                    { timeout: 5000 }
                );

                if (result.current.isSuccess) {
                    successCount++;
                }
            } catch (error) {
                // Query failed or timed out
            }

            const queryEndTime = performance.now();
            const queryTime = queryEndTime - queryStartTime;
            queryTimes.push(queryTime);

            const totalTime = queryEndTime - startTime;
            times.push(totalTime);

            unmount();

            // Small delay between iterations
            await new Promise((resolve) => setTimeout(resolve, 10));
        }

        // Calculate statistics
        const avgTotalTime = times.reduce((a, b) => a + b, 0) / times.length;
        const avgRenderTime = renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length;
        const avgQueryTime = queryTimes.reduce((a, b) => a + b, 0) / queryTimes.length;
        const minTime = Math.min(...times);
        const maxTime = Math.max(...times);
        const successRate = (successCount / this.iterations) * 100;

        // Calculate standard deviation
        const variance =
            times.reduce((acc, time) => {
                return acc + Math.pow(time - avgTotalTime, 2);
            }, 0) / times.length;
        const standardDeviation = Math.sqrt(variance);

        return {
            hookName,
            avgRenderTime,
            avgQueryTime,
            avgTotalTime,
            minTime,
            maxTime,
            standardDeviation,
            successRate,
        };
    }

    async runComparison(): Promise<{
        orpcResult: BenchmarkResult;
        axiosResult: BenchmarkResult;
        comparison: any;
    }> {
        console.log("🚀 Starting Real Hook Performance Benchmark");
        console.log(`Iterations: ${this.iterations}, Warmup: ${this.warmupRuns}`);

        // Benchmark ORPC hook
        console.log("📊 Benchmarking ORPC hook...");
        const orpcResult = await this.benchmarkHook(useFetchCurrentUser, "ORPC Hook");

        // Benchmark Axios hook
        console.log("📊 Benchmarking Axios hook...");
        const axiosResult = await this.benchmarkHook(useFetchCurrentUserOld, "Axios Hook");

        // Calculate improvements
        const comparison = {
            renderTimeImprovement:
                ((axiosResult.avgRenderTime - orpcResult.avgRenderTime) / axiosResult.avgRenderTime) * 100,
            queryTimeImprovement:
                ((axiosResult.avgQueryTime - orpcResult.avgQueryTime) / axiosResult.avgQueryTime) * 100,
            totalTimeImprovement:
                ((axiosResult.avgTotalTime - orpcResult.avgTotalTime) / axiosResult.avgTotalTime) * 100,
            reliabilityImprovement: orpcResult.successRate - axiosResult.successRate,
            consistencyImprovement:
                ((axiosResult.standardDeviation - orpcResult.standardDeviation) / axiosResult.standardDeviation) * 100,
        };

        return { orpcResult, axiosResult, comparison };
    }
}

// Test suite
describe("useFetchCurrentUser Performance Benchmark", () => {
    let benchmark: RealHookBenchmark;

    beforeEach(() => {
        benchmark = new RealHookBenchmark(150, 3); // Smaller numbers for faster tests
        vi.clearAllMocks();
    });

    afterEach(() => {
        vi.clearAllTimers();
    });

    test("should benchmark both hooks and compare performance", async () => {
        const results = await benchmark.runComparison();

        // Log results for manual inspection
        console.log("\n📊 BENCHMARK RESULTS:");
        console.log("=".repeat(50));

        console.log("\n🆕 ORPC Hook:");
        console.log(`  Avg Total Time: ${results.orpcResult.avgTotalTime.toFixed(2)}ms`);
        console.log(`  Avg Render Time: ${results.orpcResult.avgRenderTime.toFixed(2)}ms`);
        console.log(`  Avg Query Time: ${results.orpcResult.avgQueryTime.toFixed(2)}ms`);
        console.log(`  Success Rate: ${results.orpcResult.successRate.toFixed(1)}%`);
        console.log(`  Std Deviation: ${results.orpcResult.standardDeviation.toFixed(2)}ms`);

        console.log("\n🔄 Axios Hook:");
        console.log(`  Avg Total Time: ${results.axiosResult.avgTotalTime.toFixed(2)}ms`);
        console.log(`  Avg Render Time: ${results.axiosResult.avgRenderTime.toFixed(2)}ms`);
        console.log(`  Avg Query Time: ${results.axiosResult.avgQueryTime.toFixed(2)}ms`);
        console.log(`  Success Rate: ${results.axiosResult.successRate.toFixed(1)}%`);
        console.log(`  Std Deviation: ${results.axiosResult.standardDeviation.toFixed(2)}ms`);

        console.log("\n📈 Comparison:");
        console.log(`  Total Time Improvement: ${results.comparison.totalTimeImprovement.toFixed(1)}%`);
        console.log(`  Render Time Improvement: ${results.comparison.renderTimeImprovement.toFixed(1)}%`);
        console.log(`  Query Time Improvement: ${results.comparison.queryTimeImprovement.toFixed(1)}%`);
        console.log(`  Reliability Improvement: ${results.comparison.reliabilityImprovement.toFixed(1)}%`);
        console.log(`  Consistency Improvement: ${results.comparison.consistencyImprovement.toFixed(1)}%`);

        // Assertions
        expect(results.orpcResult.avgTotalTime).toBeGreaterThan(0);
        expect(results.axiosResult.avgTotalTime).toBeGreaterThan(0);
        expect(results.orpcResult.successRate).toBeGreaterThanOrEqual(0);
        expect(results.axiosResult.successRate).toBeGreaterThanOrEqual(0);

        // Performance expectations (these may need adjustment based on actual results)
        // Uncomment and adjust thresholds based on your performance requirements
        // expect(results.comparison.totalTimeImprovement).toBeGreaterThan(-20); // Allow up to 20% slower
        // expect(results.orpcResult.successRate).toBeGreaterThanOrEqual(80); // At least 80% success rate
    }, 30000); // 30 second timeout
});

// Export for standalone usage
export { RealHookBenchmark };
